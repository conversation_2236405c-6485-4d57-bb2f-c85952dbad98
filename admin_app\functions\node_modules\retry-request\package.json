{"name": "retry-request", "version": "7.0.2", "description": "Retry a request.", "main": "index.js", "repository": "stephenplusplus/retry-request", "scripts": {"docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "test": "mocha --timeout 30000", "system-test": ""}, "files": ["index.js", "index.d.ts", "license"], "types": "index.d.ts", "keywords": ["request", "retry", "stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}, "dependencies": {"@types/request": "^2.48.8", "extend": "^3.0.2", "teeny-request": "^9.0.0"}, "devDependencies": {"async": "^3.0.1", "gts": "^5.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^4.0.0", "lodash.range": "^3.2.0", "mocha": "^10.2.0", "typescript": "^5.1.6"}}