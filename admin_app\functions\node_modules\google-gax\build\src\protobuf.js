"use strict";
/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.protobufMinimal = void 0;
// This file is here to re-export protobuf.js so that the proto.js files
// produced by tools/compileProtos.ts did not depend on protobuf.js
// directly.
// Usage:
//   const {protobufMinimal} = require('google-gax/build/src/protobuf');
exports.protobufMinimal = require("protobufjs/minimal");
//# sourceMappingURL=protobuf.js.map