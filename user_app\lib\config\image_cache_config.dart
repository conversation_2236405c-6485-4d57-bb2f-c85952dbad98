import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'dart:io';

/// Global image cache configuration optimized for Android performance
class ImageCacheConfig {
  
  /// Custom cache manager for product images
  static CacheManager get productImageCacheManager => CacheManager(
    Config(
      'product_images',
      stalePeriod: const Duration(days: 7), // Cache for 7 days
      maxNrOfCacheObjects: 200, // Maximum 200 cached images
      repo: JsonCacheInfoRepository(databaseName: 'product_images'),
      fileService: HttpFileService(),
    ),
  );

  /// Custom cache manager for user profile images
  static CacheManager get profileImageCacheManager => CacheManager(
    Config(
      'profile_images',
      stalePeriod: const Duration(days: 30), // Cache for 30 days
      maxNrOfCacheObjects: 100, // Maximum 100 cached images
      repo: JsonCacheInfoRepository(databaseName: 'profile_images'),
      fileService: HttpFileService(),
    ),
  );

  /// Custom cache manager for post images
  static CacheManager get postImageCacheManager => CacheManager(
    Config(
      'post_images',
      stalePeriod: const Duration(days: 3), // Cache for 3 days
      maxNrOfCacheObjects: 150, // Maximum 150 cached images
      repo: JsonCacheInfoRepository(databaseName: 'post_images'),
      fileService: HttpFileService(),
    ),
  );

  /// Initialize cache configuration
  static void initialize() {
    // Configure image cache for Android optimization
    if (!kIsWeb && Platform.isAndroid) {
      // Set memory cache size based on device capabilities
      _configureMemoryCache();
    }
  }

  /// Configure memory cache for Android devices
  static void _configureMemoryCache() {
    // This would typically be done in native Android code
    // or through platform channels for more advanced configuration
    
    // For now, we'll rely on the CachedNetworkImage's built-in
    // memory management with our custom parameters
  }

  /// Clear all image caches
  static Future<void> clearAllCaches() async {
    await Future.wait([
      productImageCacheManager.emptyCache(),
      profileImageCacheManager.emptyCache(),
      postImageCacheManager.emptyCache(),
    ]);
  }

  /// Clear specific cache
  static Future<void> clearCache(String cacheKey) async {
    switch (cacheKey) {
      case 'products':
        await productImageCacheManager.emptyCache();
        break;
      case 'profiles':
        await profileImageCacheManager.emptyCache();
        break;
      case 'posts':
        await postImageCacheManager.emptyCache();
        break;
    }
  }

  /// Get cache size information
  static Future<Map<String, int>> getCacheSizes() async {
    final productFiles = await productImageCacheManager.getFileFromCache('');
    final profileFiles = await profileImageCacheManager.getFileFromCache('');
    final postFiles = await postImageCacheManager.getFileFromCache('');

    return {
      'products': 0, // Would need to implement actual size calculation
      'profiles': 0,
      'posts': 0,
    };
  }

  /// Preload important images
  static Future<void> preloadImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        await productImageCacheManager.downloadFile(url);
      } catch (e) {
        // Silently fail for preloading
        print('Failed to preload image: $url');
      }
    }
  }

  /// Check if image is cached
  static Future<bool> isImageCached(String imageUrl) async {
    final file = await productImageCacheManager.getFileFromCache(imageUrl);
    return file != null;
  }

  /// Get optimal cache settings for different image types
  static Map<String, dynamic> getOptimalSettings(String imageType) {
    switch (imageType) {
      case 'product_card':
        return {
          'memCacheWidth': 400,
          'memCacheHeight': 300,
          'maxWidthDiskCache': 800,
          'maxHeightDiskCache': 600,
          'quality': 80,
        };
      case 'product_preview':
        return {
          'memCacheWidth': 300,
          'memCacheHeight': 300,
          'maxWidthDiskCache': 600,
          'maxHeightDiskCache': 600,
          'quality': 75,
        };
      case 'product_detail':
        return {
          'memCacheWidth': 800,
          'memCacheHeight': 600,
          'maxWidthDiskCache': 1200,
          'maxHeightDiskCache': 900,
          'quality': 85,
        };
      case 'profile':
        return {
          'memCacheWidth': 200,
          'memCacheHeight': 200,
          'maxWidthDiskCache': 400,
          'maxHeightDiskCache': 400,
          'quality': 80,
        };
      default:
        return {
          'memCacheWidth': 400,
          'memCacheHeight': 400,
          'maxWidthDiskCache': 800,
          'maxHeightDiskCache': 800,
          'quality': 80,
        };
    }
  }
}
