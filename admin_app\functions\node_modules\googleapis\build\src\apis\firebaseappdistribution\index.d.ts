/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebaseappdistribution_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof firebaseappdistribution_v1.Firebaseappdistribution;
};
export declare function firebaseappdistribution(version: 'v1'): firebaseappdistribution_v1.Firebaseappdistribution;
export declare function firebaseappdistribution(options: firebaseappdistribution_v1.Options): firebaseappdistribution_v1.Firebaseappdistribution;
declare const auth: AuthPlus;
export { auth };
export { firebaseappdistribution_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
