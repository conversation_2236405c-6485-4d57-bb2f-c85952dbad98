{"name": "@types/request", "version": "2.48.13", "description": "TypeScript definitions for request", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/request", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "soywiz", "url": "https://github.com/soywiz"}, {"name": "bonnici", "githubUsername": "bonnici", "url": "https://github.com/bonnici"}, {"name": "<PERSON>", "githubUsername": "Bartvds", "url": "https://github.com/Bartvds"}, {"name": "<PERSON>", "githubUsername": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joeskeen"}, {"name": "<PERSON>", "githubUsername": "ccurrens", "url": "https://github.com/ccurrens"}, {"name": "<PERSON>", "githubUsername": "lookfirst", "url": "https://github.com/lookfirst"}, {"name": "<PERSON>", "githubUsername": "mastermatt", "url": "https://github.com/mastermatt"}, {"name": "<PERSON>", "githubUsername": "j<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/josecolella"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/murbanowicz"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/request"}, "scripts": {}, "dependencies": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.5"}, "peerDependencies": {}, "typesPublisherContentHash": "73ed61b08c05b6d6694d81e9999b5d72488d6fb3575e5d68ecc912a03e8ae928", "typeScriptVersion": "5.1"}