/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { civicinfo_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof civicinfo_v2.Civicinfo;
};
export declare function civicinfo(version: 'v2'): civicinfo_v2.Civicinfo;
export declare function civicinfo(options: civicinfo_v2.Options): civicinfo_v2.Civicinfo;
declare const auth: AuthPlus;
export { auth };
export { civicinfo_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
