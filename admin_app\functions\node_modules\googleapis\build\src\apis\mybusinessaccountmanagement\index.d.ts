/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessaccountmanagement_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessaccountmanagement_v1.Mybusinessaccountmanagement;
};
export declare function mybusinessaccountmanagement(version: 'v1'): mybusinessaccountmanagement_v1.Mybusinessaccountmanagement;
export declare function mybusinessaccountmanagement(options: mybusinessaccountmanagement_v1.Options): mybusinessaccountmanagement_v1.Mybusinessaccountmanagement;
declare const auth: AuthPlus;
export { auth };
export { mybusinessaccountmanagement_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
