import 'package:flutter/material.dart';
import 'services/flutter_notification_service.dart';
import 'models/notification_model.dart';

class TestNotificationWidget extends StatefulWidget {
  const TestNotificationWidget({super.key});

  @override
  State<TestNotificationWidget> createState() => _TestNotificationWidgetState();
}

class _TestNotificationWidgetState extends State<TestNotificationWidget> {
  final FlutterNotificationService _notificationService = FlutterNotificationService();
  bool _isLoading = false;
  String _result = '';
  bool _isDebugging = false;
  String _debugResult = '';

  Future<void> _debugTokens() async {
    setState(() {
      _isDebugging = true;
      _debugResult = '';
    });

    try {
      final result = await _notificationService.debugTokens();
      setState(() {
        _debugResult = 'Debug Result: ${result.toString()}';
      });
    } catch (e) {
      setState(() {
        _debugResult = 'Debug Error: $e';
      });
    } finally {
      setState(() {
        _isDebugging = false;
      });
    }
  }

  Future<void> _createDummyTokens() async {
    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final result = await _notificationService.createDummyTokens();
      setState(() {
        _result = 'Dummy Tokens Result: ${result.toString()}';
      });
    } catch (e) {
      setState(() {
        _result = 'Error creating dummy tokens: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testNotification() async {
    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final notification = NotificationModel(
        id: '',
        title: 'Test Notification',
        body: 'This is a test notification from Flutter-only service',
        type: NotificationType.broadcast,
        priority: NotificationPriority.normal,
        createdBy: 'test-admin',
        createdAt: DateTime.now(),
      );

      final result = await _notificationService.sendNotification(notification);

      setState(() {
        _result = 'Result: ${result.toString()}';
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Flutter Notification'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: _isDebugging ? null : _debugTokens,
              child: _isDebugging
                ? const CircularProgressIndicator()
                : const Text('Debug FCM Tokens'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _createDummyTokens,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              child: _isLoading
                ? const CircularProgressIndicator()
                : const Text('Create Dummy Tokens'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testNotification,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: _isLoading
                ? const CircularProgressIndicator()
                : const Text('Send Test Notification'),
            ),
            const SizedBox(height: 20),
            if (_debugResult.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Debug Info:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text(
                      _debugResult,
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    ),
                  ],
                ),
              ),
            if (_debugResult.isNotEmpty) const SizedBox(height: 20),
            if (_result.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Notification Result:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text(
                      _result,
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
