/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace domains_v1beta1 {
    export interface Options extends GlobalOptions {
        version: 'v1beta1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Domains API
     *
     * Enables management and configuration of domain names.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const domains = google.domains('v1beta1');
     * ```
     */
    export class Domains {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Defines an authorization code.
     */
    export interface Schema$AuthorizationCode {
        /**
         * The Authorization Code in ASCII. It can be used to transfer the domain to or from another registrar.
         */
        code?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * Request for the `ConfigureContactSettings` method.
     */
    export interface Schema$ConfigureContactSettingsRequest {
        /**
         * The list of contact notices that the caller acknowledges. The notices needed here depend on the values specified in `contact_settings`.
         */
        contactNotices?: string[] | null;
        /**
         * Fields of the `ContactSettings` to update.
         */
        contactSettings?: Schema$ContactSettings;
        /**
         * Required. The field mask describing which fields to update as a comma-separated list. For example, if only the registrant contact is being updated, the `update_mask` is `"registrant_contact"`.
         */
        updateMask?: string | null;
        /**
         * Validate the request without actually updating the contact settings.
         */
        validateOnly?: boolean | null;
    }
    /**
     * Request for the `ConfigureDnsSettings` method.
     */
    export interface Schema$ConfigureDnsSettingsRequest {
        /**
         * Fields of the `DnsSettings` to update.
         */
        dnsSettings?: Schema$DnsSettings;
        /**
         * Required. The field mask describing which fields to update as a comma-separated list. For example, if only the name servers are being updated for an existing Custom DNS configuration, the `update_mask` is `"custom_dns.name_servers"`. When changing the DNS provider from one type to another, pass the new provider's field name as part of the field mask. For example, when changing from a Google Domains DNS configuration to a Custom DNS configuration, the `update_mask` is `"custom_dns"`. //
         */
        updateMask?: string | null;
        /**
         * Validate the request without actually updating the DNS settings.
         */
        validateOnly?: boolean | null;
    }
    /**
     * Request for the `ConfigureManagementSettings` method.
     */
    export interface Schema$ConfigureManagementSettingsRequest {
        /**
         * Fields of the `ManagementSettings` to update.
         */
        managementSettings?: Schema$ManagementSettings;
        /**
         * Required. The field mask describing which fields to update as a comma-separated list. For example, if only the transfer lock is being updated, the `update_mask` is `"transfer_lock_state"`.
         */
        updateMask?: string | null;
    }
    /**
     * Details required for a contact associated with a `Registration`.
     */
    export interface Schema$Contact {
        /**
         * Required. Email address of the contact.
         */
        email?: string | null;
        /**
         * Fax number of the contact in international format. For example, `"******-555-0123"`.
         */
        faxNumber?: string | null;
        /**
         * Required. Phone number of the contact in international format. For example, `"******-555-0123"`.
         */
        phoneNumber?: string | null;
        /**
         * Required. Postal address of the contact.
         */
        postalAddress?: Schema$PostalAddress;
    }
    /**
     * Defines the contact information associated with a `Registration`. [ICANN](https://icann.org/) requires all domain names to have associated contact information. The `registrant_contact` is considered the domain's legal owner, and often the other contacts are identical.
     */
    export interface Schema$ContactSettings {
        /**
         * Required. The administrative contact for the `Registration`.
         */
        adminContact?: Schema$Contact;
        /**
         * Required. Privacy setting for the contacts associated with the `Registration`.
         */
        privacy?: string | null;
        /**
         * Required. The registrant contact for the `Registration`. *Caution: Anyone with access to this email address, phone number, and/or postal address can take control of the domain.* *Warning: For new `Registration`s, the registrant receives an email confirmation that they must complete within 15 days to avoid domain suspension.*
         */
        registrantContact?: Schema$Contact;
        /**
         * Required. The technical contact for the `Registration`.
         */
        technicalContact?: Schema$Contact;
    }
    /**
     * Configuration for an arbitrary DNS provider.
     */
    export interface Schema$CustomDns {
        /**
         * The list of DS records for this domain, which are used to enable DNSSEC. The domain's DNS provider can provide the values to set here. If this field is empty, DNSSEC is disabled.
         */
        dsRecords?: Schema$DsRecord[];
        /**
         * Required. A list of name servers that store the DNS zone for this domain. Each name server is a domain name, with Unicode domain names expressed in Punycode format.
         */
        nameServers?: string[] | null;
    }
    /**
     * Defines the DNS configuration of a `Registration`, including name servers, DNSSEC, and glue records.
     */
    export interface Schema$DnsSettings {
        /**
         * An arbitrary DNS provider identified by its name servers.
         */
        customDns?: Schema$CustomDns;
        /**
         * The list of glue records for this `Registration`. Commonly empty.
         */
        glueRecords?: Schema$GlueRecord[];
        /**
         * The free DNS zone provided by [Google Domains](https://domains.google/).
         */
        googleDomainsDns?: Schema$GoogleDomainsDns;
    }
    /**
     * A domain that the calling user manages in Google Domains.
     */
    export interface Schema$Domain {
        /**
         * The domain name. Unicode domain names are expressed in Punycode format.
         */
        domainName?: string | null;
        /**
         * The state of this domain as a `Registration` resource.
         */
        resourceState?: string | null;
        /**
         * Price to renew the domain for one year. Only set when `resource_state` is `IMPORTABLE`.
         */
        yearlyPrice?: Schema$Money;
    }
    /**
     * Defines a Delegation Signer (DS) record, which is needed to enable DNSSEC for a domain. It contains a digest (hash) of a DNSKEY record that must be present in the domain's DNS zone.
     */
    export interface Schema$DsRecord {
        /**
         * The algorithm used to generate the referenced DNSKEY.
         */
        algorithm?: string | null;
        /**
         * The digest generated from the referenced DNSKEY.
         */
        digest?: string | null;
        /**
         * The hash function used to generate the digest of the referenced DNSKEY.
         */
        digestType?: string | null;
        /**
         * The key tag of the record. Must be set in range 0 -- 65535.
         */
        keyTag?: number | null;
    }
    /**
     * Request for the `ExportRegistration` method.
     */
    export interface Schema$ExportRegistrationRequest {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Defines a host on your domain that is a DNS name server for your domain and/or other domains. Glue records are a way of making the IP address of a name server known, even when it serves DNS queries for its parent domain. For example, when `ns.example.com` is a name server for `example.com`, the host `ns.example.com` must have a glue record to break the circular DNS reference.
     */
    export interface Schema$GlueRecord {
        /**
         * Required. Domain name of the host in Punycode format.
         */
        hostName?: string | null;
        /**
         * List of IPv4 addresses corresponding to this host in the standard decimal format (e.g. `************`). At least one of `ipv4_address` and `ipv6_address` must be set.
         */
        ipv4Addresses?: string[] | null;
        /**
         * List of IPv6 addresses corresponding to this host in the standard hexadecimal format (e.g. `2001:db8::`). At least one of `ipv4_address` and `ipv6_address` must be set.
         */
        ipv6Addresses?: string[] | null;
    }
    /**
     * Configuration for using the free DNS zone provided by Google Domains as a `Registration`'s `dns_provider`. You cannot configure the DNS zone itself using the API. To configure the DNS zone, go to [Google Domains](https://domains.google/).
     */
    export interface Schema$GoogleDomainsDns {
        /**
         * Output only. The list of DS records published for this domain. The list is automatically populated when `ds_state` is `DS_RECORDS_PUBLISHED`, otherwise it remains empty.
         */
        dsRecords?: Schema$DsRecord[];
        /**
         * Required. The state of DS records for this domain. Used to enable or disable automatic DNSSEC.
         */
        dsState?: string | null;
        /**
         * Output only. A list of name servers that store the DNS zone for this domain. Each name server is a domain name, with Unicode domain names expressed in Punycode format. This field is automatically populated with the name servers assigned to the Google Domains DNS zone.
         */
        nameServers?: string[] | null;
    }
    /**
     * Request for the `ImportDomain` method.
     */
    export interface Schema$ImportDomainRequest {
        /**
         * Required. The domain name. Unicode domain names must be expressed in Punycode format.
         */
        domainName?: string | null;
        /**
         * Set of labels associated with the `Registration`.
         */
        labels?: {
            [key: string]: string;
        } | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response for the `ListRegistrations` method.
     */
    export interface Schema$ListRegistrationsResponse {
        /**
         * When present, there are more results to retrieve. Set `page_token` to this value on a subsequent call to get the next page of results.
         */
        nextPageToken?: string | null;
        /**
         * A list of `Registration`s.
         */
        registrations?: Schema$Registration[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Defines renewal, billing, and transfer settings for a `Registration`.
     */
    export interface Schema$ManagementSettings {
        /**
         * Output only. The renewal method for this `Registration`.
         */
        renewalMethod?: string | null;
        /**
         * Controls whether the domain can be transferred to another registrar.
         */
        transferLockState?: string | null;
    }
    /**
     * Represents an amount of money with its currency type.
     */
    export interface Schema$Money {
        /**
         * The three-letter currency code defined in ISO 4217.
         */
        currencyCode?: string | null;
        /**
         * Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.
         */
        nanos?: number | null;
        /**
         * The whole units of the amount. For example if `currencyCode` is `"USD"`, then 1 unit is one US dollar.
         */
        units?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation. Output only.
     */
    export interface Schema$OperationMetadata {
        /**
         * API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * The time the operation was created.
         */
        createTime?: string | null;
        /**
         * The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Human-readable status of the operation, if any.
         */
        statusDetail?: string | null;
        /**
         * Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Represents a postal address, e.g. for postal delivery or payments addresses. Given a postal address, a postal service can deliver items to a premise, P.O. Box or similar. It is not intended to model geographical locations (roads, towns, mountains). In typical usage an address would be created via user input or from importing existing data, depending on the type of process. Advice on address input / editing: - Use an internationalization-ready address widget such as https://github.com/google/libaddressinput) - Users should not be presented with UI elements for input or editing of fields outside countries where that field is used. For more guidance on how to use this schema, please see: https://support.google.com/business/answer/6397478
     */
    export interface Schema$PostalAddress {
        /**
         * Unstructured address lines describing the lower levels of an address. Because values in address_lines do not have type information and may sometimes contain multiple values in a single field (e.g. "Austin, TX"), it is important that the line order is clear. The order of address lines should be "envelope order" for the country/region of the address. In places where this can vary (e.g. Japan), address_language is used to make it explicit (e.g. "ja" for large-to-small ordering and "ja-Latn" or "en" for small-to-large). This way, the most specific line of an address can be selected based on the language. The minimum permitted structural representation of an address consists of a region_code with all remaining information placed in the address_lines. It would be possible to format such an address very approximately without geocoding, but no semantic reasoning could be made about any of the address components until it was at least partially resolved. Creating an address only containing a region_code and address_lines, and then geocoding is the recommended way to handle completely unstructured addresses (as opposed to guessing which parts of the address should be localities or administrative areas).
         */
        addressLines?: string[] | null;
        /**
         * Optional. Highest administrative subdivision which is used for postal addresses of a country or region. For example, this can be a state, a province, an oblast, or a prefecture. Specifically, for Spain this is the province and not the autonomous community (e.g. "Barcelona" and not "Catalonia"). Many countries don't use an administrative area in postal addresses. E.g. in Switzerland this should be left unpopulated.
         */
        administrativeArea?: string | null;
        /**
         * Optional. BCP-47 language code of the contents of this address (if known). This is often the UI language of the input form or is expected to match one of the languages used in the address' country/region, or their transliterated equivalents. This can affect formatting in certain countries, but is not critical to the correctness of the data and will never affect any validation or other non-formatting related operations. If this value is not known, it should be omitted (rather than specifying a possibly incorrect default). Examples: "zh-Hant", "ja", "ja-Latn", "en".
         */
        languageCode?: string | null;
        /**
         * Optional. Generally refers to the city/town portion of the address. Examples: US city, IT comune, UK post town. In regions of the world where localities are not well defined or do not fit into this structure well, leave locality empty and use address_lines.
         */
        locality?: string | null;
        /**
         * Optional. The name of the organization at the address.
         */
        organization?: string | null;
        /**
         * Optional. Postal code of the address. Not all countries use or require postal codes to be present, but where they are used, they may trigger additional validation with other parts of the address (e.g. state/zip validation in the U.S.A.).
         */
        postalCode?: string | null;
        /**
         * Optional. The recipient at the address. This field may, under certain circumstances, contain multiline information. For example, it might contain "care of" information.
         */
        recipients?: string[] | null;
        /**
         * Required. CLDR region code of the country/region of the address. This is never inferred and it is up to the user to ensure the value is correct. See https://cldr.unicode.org/ and https://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: "CH" for Switzerland.
         */
        regionCode?: string | null;
        /**
         * The schema revision of the `PostalAddress`. This must be set to 0, which is the latest revision. All new revisions **must** be backward compatible with old revisions.
         */
        revision?: number | null;
        /**
         * Optional. Additional, country-specific, sorting code. This is not used in most regions. Where it is used, the value is either a string like "CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number alone, representing the "sector code" (Jamaica), "delivery area indicator" (Malawi) or "post office indicator" (e.g. Côte d'Ivoire).
         */
        sortingCode?: string | null;
        /**
         * Optional. Sublocality of the address. For example, this can be neighborhoods, boroughs, districts.
         */
        sublocality?: string | null;
    }
    /**
     * Request for the `RegisterDomain` method.
     */
    export interface Schema$RegisterDomainRequest {
        /**
         * The list of contact notices that the caller acknowledges. The notices needed here depend on the values specified in `registration.contact_settings`.
         */
        contactNotices?: string[] | null;
        /**
         * The list of domain notices that you acknowledge. Call `RetrieveRegisterParameters` to see the notices that need acknowledgement.
         */
        domainNotices?: string[] | null;
        /**
         * Required. The complete `Registration` resource to be created.
         */
        registration?: Schema$Registration;
        /**
         * When true, only validation is performed, without actually registering the domain. Follows: https://cloud.google.com/apis/design/design_patterns#request_validation
         */
        validateOnly?: boolean | null;
        /**
         * Required. Yearly price to register or renew the domain. The value that should be put here can be obtained from RetrieveRegisterParameters or SearchDomains calls.
         */
        yearlyPrice?: Schema$Money;
    }
    /**
     * Parameters required to register a new domain.
     */
    export interface Schema$RegisterParameters {
        /**
         * Indicates whether the domain is available for registration. This value is accurate when obtained by calling `RetrieveRegisterParameters`, but is approximate when obtained by calling `SearchDomains`.
         */
        availability?: string | null;
        /**
         * The domain name. Unicode domain names are expressed in Punycode format.
         */
        domainName?: string | null;
        /**
         * Notices about special properties of the domain.
         */
        domainNotices?: string[] | null;
        /**
         * Contact privacy options that the domain supports.
         */
        supportedPrivacy?: string[] | null;
        /**
         * Price to register or renew the domain for one year.
         */
        yearlyPrice?: Schema$Money;
    }
    /**
     * The `Registration` resource facilitates managing and configuring domain name registrations. There are several ways to create a new `Registration` resource: To create a new `Registration` resource, find a suitable domain name by calling the `SearchDomains` method with a query to see available domain name options. After choosing a name, call `RetrieveRegisterParameters` to ensure availability and obtain information like pricing, which is needed to build a call to `RegisterDomain`. Another way to create a new `Registration` is to transfer an existing domain from another registrar. First, go to the current registrar to unlock the domain for transfer and retrieve the domain's transfer authorization code. Then call `RetrieveTransferParameters` to confirm that the domain is unlocked and to get values needed to build a call to `TransferDomain`. Finally, you can create a new `Registration` by importing an existing domain managed with [Google Domains](https://domains.google/). First, call `RetrieveImportableDomains` to list domains to which the calling user has sufficient access. Then call `ImportDomain` on any domain names you want to use with Cloud Domains.
     */
    export interface Schema$Registration {
        /**
         * Required. Settings for contact information linked to the `Registration`. You cannot update these with the `UpdateRegistration` method. To update these settings, use the `ConfigureContactSettings` method.
         */
        contactSettings?: Schema$ContactSettings;
        /**
         * Output only. The creation timestamp of the `Registration` resource.
         */
        createTime?: string | null;
        /**
         * Settings controlling the DNS configuration of the `Registration`. You cannot update these with the `UpdateRegistration` method. To update these settings, use the `ConfigureDnsSettings` method.
         */
        dnsSettings?: Schema$DnsSettings;
        /**
         * Required. Immutable. The domain name. Unicode domain names must be expressed in Punycode format.
         */
        domainName?: string | null;
        /**
         * Output only. The expiration timestamp of the `Registration`.
         */
        expireTime?: string | null;
        /**
         * Output only. The set of issues with the `Registration` that require attention.
         */
        issues?: string[] | null;
        /**
         * Set of labels associated with the `Registration`.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Settings for management of the `Registration`, including renewal, billing, and transfer. You cannot update these with the `UpdateRegistration` method. To update these settings, use the `ConfigureManagementSettings` method.
         */
        managementSettings?: Schema$ManagementSettings;
        /**
         * Output only. Name of the `Registration` resource, in the format `projects/x/locations/x/registrations/`.
         */
        name?: string | null;
        /**
         * Output only. Pending contact settings for the `Registration`. Updates to the `contact_settings` field that change its `registrant_contact` or `privacy` fields require email confirmation by the `registrant_contact` before taking effect. This field is set only if there are pending updates to the `contact_settings` that have not been confirmed. To confirm the changes, the `registrant_contact` must follow the instructions in the email they receive.
         */
        pendingContactSettings?: Schema$ContactSettings;
        /**
         * Output only. The reason the domain registration failed. Only set for domains in REGISTRATION_FAILED state.
         */
        registerFailureReason?: string | null;
        /**
         * Output only. The state of the `Registration`
         */
        state?: string | null;
        /**
         * Output only. Set of options for the `contact_settings.privacy` field that this `Registration` supports.
         */
        supportedPrivacy?: string[] | null;
        /**
         * Output only. The reason the domain transfer failed. Only set for domains in TRANSFER_FAILED state.
         */
        transferFailureReason?: string | null;
    }
    /**
     * Request for the `ResetAuthorizationCode` method.
     */
    export interface Schema$ResetAuthorizationCodeRequest {
    }
    /**
     * Response for the `RetrieveImportableDomains` method.
     */
    export interface Schema$RetrieveImportableDomainsResponse {
        /**
         * A list of domains that the calling user manages in Google Domains.
         */
        domains?: Schema$Domain[];
        /**
         * When present, there are more results to retrieve. Set `page_token` to this value on a subsequent call to get the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response for the `RetrieveRegisterParameters` method.
     */
    export interface Schema$RetrieveRegisterParametersResponse {
        /**
         * Parameters to use when calling the `RegisterDomain` method.
         */
        registerParameters?: Schema$RegisterParameters;
    }
    /**
     * Response for the `RetrieveTransferParameters` method.
     */
    export interface Schema$RetrieveTransferParametersResponse {
        /**
         * Parameters to use when calling the `TransferDomain` method.
         */
        transferParameters?: Schema$TransferParameters;
    }
    /**
     * Response for the `SearchDomains` method.
     */
    export interface Schema$SearchDomainsResponse {
        /**
         * Results of the domain name search.
         */
        registerParameters?: Schema$RegisterParameters[];
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Request for the `TransferDomain` method.
     */
    export interface Schema$TransferDomainRequest {
        /**
         * The domain's transfer authorization code. You can obtain this from the domain's current registrar.
         */
        authorizationCode?: Schema$AuthorizationCode;
        /**
         * The list of contact notices that you acknowledge. The notices needed here depend on the values specified in `registration.contact_settings`.
         */
        contactNotices?: string[] | null;
        /**
         * Required. The complete `Registration` resource to be created. You can leave `registration.dns_settings` unset to import the domain's current DNS configuration from its current registrar. Use this option only if you are sure that the domain's current DNS service does not cease upon transfer, as is often the case for DNS services provided for free by the registrar.
         */
        registration?: Schema$Registration;
        /**
         * Validate the request without actually transferring the domain.
         */
        validateOnly?: boolean | null;
        /**
         * Required. Acknowledgement of the price to transfer or renew the domain for one year. Call `RetrieveTransferParameters` to obtain the price, which you must acknowledge.
         */
        yearlyPrice?: Schema$Money;
    }
    /**
     * Parameters required to transfer a domain from another registrar.
     */
    export interface Schema$TransferParameters {
        /**
         * The registrar that currently manages the domain.
         */
        currentRegistrar?: string | null;
        /**
         * The URL of the registrar that currently manages the domain.
         */
        currentRegistrarUri?: string | null;
        /**
         * The domain name. Unicode domain names are expressed in Punycode format.
         */
        domainName?: string | null;
        /**
         * The name servers that currently store the configuration of the domain.
         */
        nameServers?: string[] | null;
        /**
         * Contact privacy options that the domain supports.
         */
        supportedPrivacy?: string[] | null;
        /**
         * Indicates whether the domain is protected by a transfer lock. For a transfer to succeed, this must show `UNLOCKED`. To unlock a domain, go to its current registrar.
         */
        transferLockState?: string | null;
        /**
         * Price to transfer or renew the domain for one year.
         */
        yearlyPrice?: Schema$Money;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        operations: Resource$Projects$Locations$Operations;
        registrations: Resource$Projects$Locations$Registrations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Registrations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Updates a `Registration`'s contact settings. Some changes require confirmation by the domain's registrant contact .
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        configureContactSettings(params: Params$Resource$Projects$Locations$Registrations$Configurecontactsettings, options: StreamMethodOptions): GaxiosPromise<Readable>;
        configureContactSettings(params?: Params$Resource$Projects$Locations$Registrations$Configurecontactsettings, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        configureContactSettings(params: Params$Resource$Projects$Locations$Registrations$Configurecontactsettings, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        configureContactSettings(params: Params$Resource$Projects$Locations$Registrations$Configurecontactsettings, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        configureContactSettings(params: Params$Resource$Projects$Locations$Registrations$Configurecontactsettings, callback: BodyResponseCallback<Schema$Operation>): void;
        configureContactSettings(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a `Registration`'s DNS settings.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        configureDnsSettings(params: Params$Resource$Projects$Locations$Registrations$Configurednssettings, options: StreamMethodOptions): GaxiosPromise<Readable>;
        configureDnsSettings(params?: Params$Resource$Projects$Locations$Registrations$Configurednssettings, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        configureDnsSettings(params: Params$Resource$Projects$Locations$Registrations$Configurednssettings, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        configureDnsSettings(params: Params$Resource$Projects$Locations$Registrations$Configurednssettings, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        configureDnsSettings(params: Params$Resource$Projects$Locations$Registrations$Configurednssettings, callback: BodyResponseCallback<Schema$Operation>): void;
        configureDnsSettings(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a `Registration`'s management settings.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        configureManagementSettings(params: Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings, options: StreamMethodOptions): GaxiosPromise<Readable>;
        configureManagementSettings(params?: Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        configureManagementSettings(params: Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        configureManagementSettings(params: Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        configureManagementSettings(params: Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings, callback: BodyResponseCallback<Schema$Operation>): void;
        configureManagementSettings(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a `Registration` resource. This method works on any `Registration` resource using [Subscription or Commitment billing](/domains/pricing#billing-models), provided that the resource was created at least 1 day in the past. For `Registration` resources using [Monthly billing](/domains/pricing#billing-models), this method works if: * `state` is `EXPORTED` with `expire_time` in the past * `state` is `REGISTRATION_FAILED` * `state` is `TRANSFER_FAILED` When an active registration is successfully deleted, you can continue to use the domain in [Google Domains](https://domains.google/) until it expires. The calling user becomes the domain's sole owner in Google Domains, and permissions for the domain are subsequently managed there. The domain does not renew automatically unless the new owner sets up billing in Google Domains.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Registrations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Registrations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Registrations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Registrations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Registrations$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Exports a `Registration` resource, such that it is no longer managed by Cloud Domains. When an active domain is successfully exported, you can continue to use the domain in [Google Domains](https://domains.google/) until it expires. The calling user becomes the domain's sole owner in Google Domains, and permissions for the domain are subsequently managed there. The domain does not renew automatically unless the new owner sets up billing in Google Domains.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        export(params: Params$Resource$Projects$Locations$Registrations$Export, options: StreamMethodOptions): GaxiosPromise<Readable>;
        export(params?: Params$Resource$Projects$Locations$Registrations$Export, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        export(params: Params$Resource$Projects$Locations$Registrations$Export, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        export(params: Params$Resource$Projects$Locations$Registrations$Export, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        export(params: Params$Resource$Projects$Locations$Registrations$Export, callback: BodyResponseCallback<Schema$Operation>): void;
        export(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a `Registration` resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Registrations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Registrations$Get, options?: MethodOptions): GaxiosPromise<Schema$Registration>;
        get(params: Params$Resource$Projects$Locations$Registrations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Registrations$Get, options: MethodOptions | BodyResponseCallback<Schema$Registration>, callback: BodyResponseCallback<Schema$Registration>): void;
        get(params: Params$Resource$Projects$Locations$Registrations$Get, callback: BodyResponseCallback<Schema$Registration>): void;
        get(callback: BodyResponseCallback<Schema$Registration>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Registrations$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Imports a domain name from [Google Domains](https://domains.google/) for use in Cloud Domains. To transfer a domain from another registrar, use the `TransferDomain` method instead. Since individual users can own domains in Google Domains, the calling user must have ownership permission on the domain.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        import(params: Params$Resource$Projects$Locations$Registrations$Import, options: StreamMethodOptions): GaxiosPromise<Readable>;
        import(params?: Params$Resource$Projects$Locations$Registrations$Import, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        import(params: Params$Resource$Projects$Locations$Registrations$Import, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        import(params: Params$Resource$Projects$Locations$Registrations$Import, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        import(params: Params$Resource$Projects$Locations$Registrations$Import, callback: BodyResponseCallback<Schema$Operation>): void;
        import(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists the `Registration` resources in a project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Registrations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Registrations$List, options?: MethodOptions): GaxiosPromise<Schema$ListRegistrationsResponse>;
        list(params: Params$Resource$Projects$Locations$Registrations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Registrations$List, options: MethodOptions | BodyResponseCallback<Schema$ListRegistrationsResponse>, callback: BodyResponseCallback<Schema$ListRegistrationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Registrations$List, callback: BodyResponseCallback<Schema$ListRegistrationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRegistrationsResponse>): void;
        /**
         * Updates select fields of a `Registration` resource, notably `labels`. To update other fields, use the appropriate custom update method: * To update management settings, see `ConfigureManagementSettings` * To update DNS configuration, see `ConfigureDnsSettings` * To update contact information, see `ConfigureContactSettings`
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Registrations$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Registrations$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Registrations$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Registrations$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Registrations$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Registers a new domain name and creates a corresponding `Registration` resource. Call `RetrieveRegisterParameters` first to check availability of the domain name and determine parameters like price that are needed to build a call to this method. A successful call creates a `Registration` resource in state `REGISTRATION_PENDING`, which resolves to `ACTIVE` within 1-2 minutes, indicating that the domain was successfully registered. If the resource ends up in state `REGISTRATION_FAILED`, it indicates that the domain was not registered successfully, and you can safely delete the resource and retry registration.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        register(params: Params$Resource$Projects$Locations$Registrations$Register, options: StreamMethodOptions): GaxiosPromise<Readable>;
        register(params?: Params$Resource$Projects$Locations$Registrations$Register, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        register(params: Params$Resource$Projects$Locations$Registrations$Register, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        register(params: Params$Resource$Projects$Locations$Registrations$Register, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        register(params: Params$Resource$Projects$Locations$Registrations$Register, callback: BodyResponseCallback<Schema$Operation>): void;
        register(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resets the authorization code of the `Registration` to a new random string. You can call this method only after 60 days have elapsed since the initial domain registration.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resetAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode, options: StreamMethodOptions): GaxiosPromise<Readable>;
        resetAuthorizationCode(params?: Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode, options?: MethodOptions): GaxiosPromise<Schema$AuthorizationCode>;
        resetAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resetAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode, options: MethodOptions | BodyResponseCallback<Schema$AuthorizationCode>, callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        resetAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode, callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        resetAuthorizationCode(callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        /**
         * Gets the authorization code of the `Registration` for the purpose of transferring the domain to another registrar. You can call this method only after 60 days have elapsed since the initial domain registration.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        retrieveAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode, options: StreamMethodOptions): GaxiosPromise<Readable>;
        retrieveAuthorizationCode(params?: Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode, options?: MethodOptions): GaxiosPromise<Schema$AuthorizationCode>;
        retrieveAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        retrieveAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode, options: MethodOptions | BodyResponseCallback<Schema$AuthorizationCode>, callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        retrieveAuthorizationCode(params: Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode, callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        retrieveAuthorizationCode(callback: BodyResponseCallback<Schema$AuthorizationCode>): void;
        /**
         * Lists domain names from [Google Domains](https://domains.google/) that can be imported to Cloud Domains using the `ImportDomain` method. Since individual users can own domains in Google Domains, the list of domains returned depends on the individual user making the call. Domains already managed by Cloud Domains are not returned.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        retrieveImportableDomains(params: Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains, options: StreamMethodOptions): GaxiosPromise<Readable>;
        retrieveImportableDomains(params?: Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains, options?: MethodOptions): GaxiosPromise<Schema$RetrieveImportableDomainsResponse>;
        retrieveImportableDomains(params: Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        retrieveImportableDomains(params: Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains, options: MethodOptions | BodyResponseCallback<Schema$RetrieveImportableDomainsResponse>, callback: BodyResponseCallback<Schema$RetrieveImportableDomainsResponse>): void;
        retrieveImportableDomains(params: Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains, callback: BodyResponseCallback<Schema$RetrieveImportableDomainsResponse>): void;
        retrieveImportableDomains(callback: BodyResponseCallback<Schema$RetrieveImportableDomainsResponse>): void;
        /**
         * Gets parameters needed to register a new domain name, including price and up-to-date availability. Use the returned values to call `RegisterDomain`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        retrieveRegisterParameters(params: Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters, options: StreamMethodOptions): GaxiosPromise<Readable>;
        retrieveRegisterParameters(params?: Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters, options?: MethodOptions): GaxiosPromise<Schema$RetrieveRegisterParametersResponse>;
        retrieveRegisterParameters(params: Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        retrieveRegisterParameters(params: Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters, options: MethodOptions | BodyResponseCallback<Schema$RetrieveRegisterParametersResponse>, callback: BodyResponseCallback<Schema$RetrieveRegisterParametersResponse>): void;
        retrieveRegisterParameters(params: Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters, callback: BodyResponseCallback<Schema$RetrieveRegisterParametersResponse>): void;
        retrieveRegisterParameters(callback: BodyResponseCallback<Schema$RetrieveRegisterParametersResponse>): void;
        /**
         * Gets parameters needed to transfer a domain name from another registrar to Cloud Domains. For domains already managed by [Google Domains](https://domains.google/), use `ImportDomain` instead. Use the returned values to call `TransferDomain`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        retrieveTransferParameters(params: Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters, options: StreamMethodOptions): GaxiosPromise<Readable>;
        retrieveTransferParameters(params?: Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters, options?: MethodOptions): GaxiosPromise<Schema$RetrieveTransferParametersResponse>;
        retrieveTransferParameters(params: Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        retrieveTransferParameters(params: Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters, options: MethodOptions | BodyResponseCallback<Schema$RetrieveTransferParametersResponse>, callback: BodyResponseCallback<Schema$RetrieveTransferParametersResponse>): void;
        retrieveTransferParameters(params: Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters, callback: BodyResponseCallback<Schema$RetrieveTransferParametersResponse>): void;
        retrieveTransferParameters(callback: BodyResponseCallback<Schema$RetrieveTransferParametersResponse>): void;
        /**
         * Searches for available domain names similar to the provided query. Availability results from this method are approximate; call `RetrieveRegisterParameters` on a domain before registering to confirm availability.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        searchDomains(params: Params$Resource$Projects$Locations$Registrations$Searchdomains, options: StreamMethodOptions): GaxiosPromise<Readable>;
        searchDomains(params?: Params$Resource$Projects$Locations$Registrations$Searchdomains, options?: MethodOptions): GaxiosPromise<Schema$SearchDomainsResponse>;
        searchDomains(params: Params$Resource$Projects$Locations$Registrations$Searchdomains, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        searchDomains(params: Params$Resource$Projects$Locations$Registrations$Searchdomains, options: MethodOptions | BodyResponseCallback<Schema$SearchDomainsResponse>, callback: BodyResponseCallback<Schema$SearchDomainsResponse>): void;
        searchDomains(params: Params$Resource$Projects$Locations$Registrations$Searchdomains, callback: BodyResponseCallback<Schema$SearchDomainsResponse>): void;
        searchDomains(callback: BodyResponseCallback<Schema$SearchDomainsResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Registrations$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Registrations$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Registrations$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Registrations$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Registrations$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Registrations$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Registrations$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Transfers a domain name from another registrar to Cloud Domains. For domains already managed by [Google Domains](https://domains.google/), use `ImportDomain` instead. Before calling this method, go to the domain's current registrar to unlock the domain for transfer and retrieve the domain's transfer authorization code. Then call `RetrieveTransferParameters` to confirm that the domain is unlocked and to get values needed to build a call to this method. A successful call creates a `Registration` resource in state `TRANSFER_PENDING`. It can take several days to complete the transfer process. The registrant can often speed up this process by approving the transfer through the current registrar, either by clicking a link in an email from the registrar or by visiting the registrar's website. A few minutes after transfer approval, the resource transitions to state `ACTIVE`, indicating that the transfer was successful. If the transfer is rejected or the request expires without being approved, the resource can end up in state `TRANSFER_FAILED`. If transfer fails, you can safely delete the resource and retry the transfer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        transfer(params: Params$Resource$Projects$Locations$Registrations$Transfer, options: StreamMethodOptions): GaxiosPromise<Readable>;
        transfer(params?: Params$Resource$Projects$Locations$Registrations$Transfer, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        transfer(params: Params$Resource$Projects$Locations$Registrations$Transfer, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        transfer(params: Params$Resource$Projects$Locations$Registrations$Transfer, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        transfer(params: Params$Resource$Projects$Locations$Registrations$Transfer, callback: BodyResponseCallback<Schema$Operation>): void;
        transfer(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Configurecontactsettings extends StandardParameters {
        /**
         * Required. The name of the `Registration` whose contact settings are being updated, in the format `projects/x/locations/x/registrations/x`.
         */
        registration?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ConfigureContactSettingsRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Configurednssettings extends StandardParameters {
        /**
         * Required. The name of the `Registration` whose DNS settings are being updated, in the format `projects/x/locations/x/registrations/x`.
         */
        registration?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ConfigureDnsSettingsRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Configuremanagementsettings extends StandardParameters {
        /**
         * Required. The name of the `Registration` whose management settings are being updated, in the format `projects/x/locations/x/registrations/x`.
         */
        registration?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ConfigureManagementSettingsRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Delete extends StandardParameters {
        /**
         * Required. The name of the `Registration` to delete, in the format `projects/x/locations/x/registrations/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Export extends StandardParameters {
        /**
         * Required. The name of the `Registration` to export, in the format `projects/x/locations/x/registrations/x`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ExportRegistrationRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Get extends StandardParameters {
        /**
         * Required. The name of the `Registration` to get, in the format `projects/x/locations/x/registrations/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Import extends StandardParameters {
        /**
         * Required. The parent resource of the Registration. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ImportDomainRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$List extends StandardParameters {
        /**
         * Filter expression to restrict the `Registration`s returned. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, a boolean, or an enum value. The comparison operator should be one of =, !=, \>, <, \>=, <=, or : for prefix or wildcard matches. For example, to filter to a specific domain name, use an expression like `domainName="example.com"`. You can also check for the existence of a field; for example, to find domains using custom DNS settings, use an expression like `dnsSettings.customDns:*`. You can also create compound filters by combining expressions with the `AND` and `OR` operators. For example, to find domains that are suspended or have specific issues flagged, use an expression like `(state=SUSPENDED) OR (issue:*)`.
         */
        filter?: string;
        /**
         * Maximum number of results to return.
         */
        pageSize?: number;
        /**
         * When set to the `next_page_token` from a prior response, provides the next page of results.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which to list `Registration`s, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Patch extends StandardParameters {
        /**
         * Output only. Name of the `Registration` resource, in the format `projects/x/locations/x/registrations/`.
         */
        name?: string;
        /**
         * Required. The field mask describing which fields to update as a comma-separated list. For example, if only the labels are being updated, the `update_mask` is `"labels"`.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Registration;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Register extends StandardParameters {
        /**
         * Required. The parent resource of the `Registration`. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RegisterDomainRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Resetauthorizationcode extends StandardParameters {
        /**
         * Required. The name of the `Registration` whose authorization code is being reset, in the format `projects/x/locations/x/registrations/x`.
         */
        registration?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResetAuthorizationCodeRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Retrieveauthorizationcode extends StandardParameters {
        /**
         * Required. The name of the `Registration` whose authorization code is being retrieved, in the format `projects/x/locations/x/registrations/x`.
         */
        registration?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Retrieveimportabledomains extends StandardParameters {
        /**
         * Required. The location. Must be in the format `projects/x/locations/x`.
         */
        location?: string;
        /**
         * Maximum number of results to return.
         */
        pageSize?: number;
        /**
         * When set to the `next_page_token` from a prior response, provides the next page of results.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Retrieveregisterparameters extends StandardParameters {
        /**
         * Required. The domain name. Unicode domain names must be expressed in Punycode format.
         */
        domainName?: string;
        /**
         * Required. The location. Must be in the format `projects/x/locations/x`.
         */
        location?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Retrievetransferparameters extends StandardParameters {
        /**
         * Required. The domain name. Unicode domain names must be expressed in Punycode format.
         */
        domainName?: string;
        /**
         * Required. The location. Must be in the format `projects/x/locations/x`.
         */
        location?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Searchdomains extends StandardParameters {
        /**
         * Required. The location. Must be in the format `projects/x/locations/x`.
         */
        location?: string;
        /**
         * Required. String used to search for available domain names.
         */
        query?: string;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Registrations$Transfer extends StandardParameters {
        /**
         * Required. The parent resource of the `Registration`. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TransferDomainRequest;
    }
    export {};
}
