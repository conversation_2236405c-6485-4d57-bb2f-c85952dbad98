{"version": 3, "file": "fromproto3json.js", "sourceRoot": "", "sources": ["../../typescript/src/fromproto3json.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAGjC,+BAAsD;AACtD,mCAA4C;AAC5C,iCAAgD;AAEhD,mCAIiB;AACjB,iCAAuE;AACvE,yCAAgE;AAChE,2CAAkE;AAClE,yCAAiD;AACjD,2CAAkE;AAElE,SAAgB,sCAAsC,CACpD,IAA4C,EAC5C,IAAe;IAEf,MAAM,sBAAsB,GAC1B,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,gCAAyB,EAAC,IAAI,CAAC,CAAC;IAEpE,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE;QAChD,kBAAkB;QAClB,IAAI,sBAAsB,KAAK,4BAA4B,EAAE;YAC3D,OAAO,YAAY,CAAC;SACrB;QAED,OAAO,IAAA,+BAAwB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC7C;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,IAAI,CAAC;KACb;IAED,mDAAmD;IACnD,kEAAkE;IAElE,8CAA8C;IAC9C,IAAI,sBAAsB,KAAK,wBAAwB,EAAE;QACvD,OAAO,IAAA,yCAAiC,EAAC,IAAI,CAAC,CAAC;KAChD;IAED,IAAI,mBAAY,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE;QAC5C,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACtE,MAAM,IAAI,KAAK,CACb,mEAAmE,sBAAsB,sDAAsD,OAAO,IAAI,EAAE,CAC7J,CAAC;SACH;QACD,OAAO,IAAA,gCAAqB,EAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;KAC5D;IAED,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;KACb;IAED,8BAA8B;IAC9B,IAAI,sBAAsB,KAAK,sBAAsB,EAAE;QACrD,OAAO,IAAA,qCAA+B,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACzD;IAED,IAAI,sBAAsB,KAAK,yBAAyB,EAAE;QACxD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,4FAA4F,OAAO,IAAI,EAAE,CAC1G,CAAC;SACH;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CACb,mGAAmG,CACpG,CAAC;SACH;QACD,OAAO,IAAA,0CAAkC,EAAC,IAAI,CAAC,CAAC;KACjD;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE;QAC3D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;SACH;QACD,OAAO,IAAA,6CAAqC,EAAC,IAAI,CAAC,CAAC;KACpD;IAED,IAAI,sBAAsB,KAAK,2BAA2B,EAAE;QAC1D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,6FAA6F,OAAO,IAAI,EAAE,CAC3G,CAAC;SACH;QACD,OAAO,IAAA,+CAAoC,EAAC,IAAI,CAAC,CAAC;KACnD;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE;QAC3D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;SACH;QACD,OAAO,IAAA,iDAAqC,EAAC,IAAI,CAAC,CAAC;KACpD;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE;QAC3D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;SACH;QACD,OAAO,IAAA,iDAAqC,EAAC,IAAI,CAAC,CAAC;KACpD;IAED,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,EAAE;YACV,SAAS;SACV;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACxC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAE7B,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClB,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;aAClB;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACzB,MAAM,IAAI,KAAK,CACb,uEAAuE,GAAG,EAAE,CAC7E,CAAC;iBACH;gBACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAChC,sCAAsC,CACpC,YAAY,IAAI,SAAS,EACzB,OAAO,CACR,CACF,CAAC;aACH;SACF;aAAM,IAAI,KAAK,CAAC,GAAG,EAAE;YACpB,MAAM,GAAG,GAAoB,EAAE,CAAC;YAChC,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtD,GAAG,CAAC,MAAM,CAAC,GAAG,sCAAsC,CAClD,YAAY,IAAI,SAAS,EACzB,QAAqB,CACtB,CAAC;aACH;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACnB;aAAM,IACL,SAAS,CAAC,KAAK,CAAC,iDAAiD,CAAC,EAClE;YACA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC1D,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;aACH;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;aAAM,IAAI,SAAS,KAAK,QAAQ,EAAE;YACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;aACH;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;aAAM,IAAI,SAAS,KAAK,MAAM,EAAE;YAC/B,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;aACH;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;aAAM,IAAI,SAAS,KAAK,OAAO,EAAE;YAChC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;aACH;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,2BAAmB,EAAC,KAAK,CAAC,CAAC;SAC1C;aAAM;YACL,eAAe;YACf,IAAA,aAAM,EACJ,YAAY,KAAK,IAAI,EACrB,iDAAiD,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAC;YACF,MAAM,iBAAiB,GAAG,sCAAsC,CAC9D,YAAa,EACb,KAAK,CACN,CAAC;YACF,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;SACjC;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AApLD,wFAoLC;AAED,SAAgB,cAAc,CAAC,IAAmB,EAAE,IAAe;IACjE,MAAM,YAAY,GAAG,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxE,IAAI,YAAY,KAAK,IAAI,EAAE;QACzB,OAAO,IAAI,CAAC;KACb;IACD,4HAA4H;IAC5H,IAAA,aAAM,EACJ,OAAO,YAAY,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAChE,2CAA2C,IAAI,EAAE,CAClD,CAAC;IACF,OAAO,IAAI,CAAC,UAAU,CAAC,YAAkB,CAAC,CAAC;AAC7C,CAAC;AAXD,wCAWC"}