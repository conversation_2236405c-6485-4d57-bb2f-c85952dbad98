import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'screens/main_navigation_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/pending_registration_screen.dart';
import 'screens/navigation_demo_screen.dart';
import 'screens/spare_parts_screen.dart';
import 'screens/coming_soon_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/help_screen.dart';
import 'screens/support_screen.dart';
import 'screens/reseller_list_screen.dart';
import 'providers/auth_provider.dart';
import 'firebase_options.dart';
import 'config/image_cache_config.dart';
import 'utils/performance_monitor.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize image cache configuration for Android optimization
  ImageCacheConfig.initialize();

  // Start performance monitoring for image loading
  PerformanceMonitor.startPeriodicLogging();

  runApp(const AmalPointApp());
}

class AmalPointApp extends StatelessWidget {
  const AmalPointApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        home: const SplashScreen(),
        routes: {
          '/splash': (context) => const SplashScreen(),
          '/main': (context) => const MainNavigationScreen(),
          '/login': (context) => const LoginScreen(),
          '/pending-registration': (context) => const PendingRegistrationScreen(),
          '/navigation-demo': (context) => const NavigationDemoScreen(),
          '/spare-parts': (context) => const SparePartsScreen(),
          '/coming-soon': (context) => const ComingSoonScreen(),
          '/help': (context) => const HelpScreen(),
          '/support': (context) => const SupportScreen(),
          '/resellers': (context) => const ResellerListScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}


