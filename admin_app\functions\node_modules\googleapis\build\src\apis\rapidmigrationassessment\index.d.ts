/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { rapidmigrationassessment_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof rapidmigrationassessment_v1.Rapidmigrationassessment;
};
export declare function rapidmigrationassessment(version: 'v1'): rapidmigrationassessment_v1.Rapidmigrationassessment;
export declare function rapidmigrationassessment(options: rapidmigrationassessment_v1.Options): rapidmigrationassessment_v1.Rapidmigrationassessment;
declare const auth: AuthPlus;
export { auth };
export { rapidmigrationassessment_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
