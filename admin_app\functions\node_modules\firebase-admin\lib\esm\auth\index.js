import mod from "../../auth/index.js";

export const Auth = mod.Auth;
export const AuthClientErrorCode = mod.AuthClientErrorCode;
export const BaseAuth = mod.BaseAuth;
export const FirebaseAuthError = mod.FirebaseAuthError;
export const MultiFactorInfo = mod.MultiFactorInfo;
export const MultiFactorSettings = mod.MultiFactorSettings;
export const PhoneMultiFactorInfo = mod.PhoneMultiFactorInfo;
export const ProjectConfig = mod.ProjectConfig;
export const ProjectConfigManager = mod.ProjectConfigManager;
export const Tenant = mod.Tenant;
export const TenantAwareAuth = mod.TenantAwareAuth;
export const TenantManager = mod.TenantManager;
export const UserInfo = mod.UserInfo;
export const UserMetadata = mod.UserMetadata;
export const UserRecord = mod.UserRecord;
export const getAuth = mod.getAuth;
