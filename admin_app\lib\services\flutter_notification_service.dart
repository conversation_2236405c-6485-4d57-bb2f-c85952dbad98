import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/notification_model.dart';
import '../models/fcm_token_model.dart';
import '../models/user_model.dart';

class FlutterNotificationService {
  static final FlutterNotificationService _instance = FlutterNotificationService._internal();
  factory FlutterNotificationService() => _instance;
  FlutterNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Firebase project configuration
  static const String _projectId = 'play-integrity-snipydrrvihjxfz';
  static const String _fcmScope = 'https://www.googleapis.com/auth/firebase.messaging';

  // Load service account from assets
  Future<Map<String, dynamic>> _loadServiceAccount() async {
    try {
      final String serviceAccountJson = await rootBundle.loadString('assets/firebase-service-account.json');
      return json.decode(serviceAccountJson);
    } catch (e) {
      throw Exception('Failed to load service account: $e');
    }
  }

  // Generate OAuth2 access token using JWT
  Future<String> _getAccessToken() async {
    try {
      final serviceAccount = await _loadServiceAccount();
      
      final jwt = JWT({
        'iss': serviceAccount['client_email'],
        'scope': _fcmScope,
        'aud': 'https://oauth2.googleapis.com/token',
        'exp': DateTime.now().add(Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
        'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      });

      final token = jwt.sign(
        RSAPrivateKey(serviceAccount['private_key']),
        algorithm: JWTAlgorithm.RS256,
      );

      // Exchange JWT for access token
      final response = await http.post(
        Uri.parse('https://oauth2.googleapis.com/token'),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          'assertion': token,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['access_token'];
      } else {
        throw Exception('Failed to get access token: ${response.body}');
      }
    } catch (e) {
      throw Exception('Authentication failed: $e');
    }
  }

  // Send FCM message using v1 API
  Future<Map<String, dynamic>> _sendFCMMessage({
    required String token,
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      
      final message = {
        'message': {
          'token': token,
          'notification': {
            'title': title,
            'body': body,
            if (imageUrl != null) 'image': imageUrl,
          },
          if (data != null) 'data': data.map((key, value) => MapEntry(key, value.toString())),
          'android': {
            'notification': {
              'click_action': 'FLUTTER_NOTIFICATION_CLICK',
              'channel_id': 'high_importance_channel',
            }
          },
          'apns': {
            'payload': {
              'aps': {
                'category': 'GENERAL',
                'sound': 'default',
              }
            }
          }
        }
      };

      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/v1/projects/$_projectId/messages:send'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: json.encode(message),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'response': json.decode(response.body),
        };
      } else {
        final errorData = json.decode(response.body);
        return {
          'success': false,
          'error': errorData,
          'statusCode': response.statusCode,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Get FCM tokens based on notification type
  Future<List<String>> _getTargetTokens(NotificationModel notification) async {
    List<String> tokens = [];

    try {
      Query<Map<String, dynamic>> tokensQuery = _firestore
          .collection('fcm_tokens')
          .where('isActive', isEqualTo: true);

      if (notification.type == NotificationType.targeted && notification.targetUserIds.isNotEmpty) {
        // Get tokens for specific users
        tokensQuery = tokensQuery.where('userId', whereIn: notification.targetUserIds);
      } else if (notification.type == NotificationType.role && notification.targetRoles.isNotEmpty) {
        // Get users with specific roles first
        final usersQuery = await _firestore
            .collection('users')
            .where('role', whereIn: notification.targetRoles)
            .get();

        final userIds = usersQuery.docs.map((doc) => doc.id).toList();
        if (userIds.isNotEmpty) {
          tokensQuery = tokensQuery.where('userId', whereIn: userIds);
        } else {
          return []; // No users found with specified roles
        }
      }
      // For broadcast notifications, no additional filtering needed

      final tokensSnapshot = await tokensQuery.get();
      tokens = tokensSnapshot.docs
          .map((doc) => doc.data()['token'] as String)
          .where((token) => token.isNotEmpty)
          .toList();

    } catch (e) {
      print('Error getting target tokens: $e');
    }

    return tokens;
  }

  // Debug method to check tokens
  Future<Map<String, dynamic>> debugTokens() async {
    try {
      final tokensSnapshot = await _firestore
          .collection('fcm_tokens')
          .get();

      final activeTokensSnapshot = await _firestore
          .collection('fcm_tokens')
          .where('isActive', isEqualTo: true)
          .get();

      return {
        'totalTokens': tokensSnapshot.docs.length,
        'activeTokens': activeTokensSnapshot.docs.length,
        'tokens': tokensSnapshot.docs.map((doc) => {
          'id': doc.id,
          'data': doc.data(),
        }).toList(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  // Create dummy tokens for testing
  Future<Map<String, dynamic>> createDummyTokens() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Create some dummy tokens for testing
      final dummyTokens = [
        'dummy_token_1_${DateTime.now().millisecondsSinceEpoch}',
        'dummy_token_2_${DateTime.now().millisecondsSinceEpoch}',
        'dummy_token_3_${DateTime.now().millisecondsSinceEpoch}',
      ];

      final batch = _firestore.batch();

      for (int i = 0; i < dummyTokens.length; i++) {
        final tokenRef = _firestore.collection('fcm_tokens').doc();
        batch.set(tokenRef, {
          'userId': 'test_user_$i',
          'token': dummyTokens[i],
          'platform': 'web',
          'deviceId': 'test_device_$i',
          'createdAt': FieldValue.serverTimestamp(),
          'lastUsed': FieldValue.serverTimestamp(),
          'isActive': true,
          'deviceInfo': {
            'platform': 'web',
            'timestamp': DateTime.now().toIso8601String(),
          }
        });
      }

      await batch.commit();

      return {
        'success': true,
        'message': 'Created ${dummyTokens.length} dummy tokens',
        'tokens': dummyTokens,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Send notification to multiple users
  Future<Map<String, dynamic>> sendNotification(NotificationModel notification) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'error': 'User not authenticated',
        };
      }

      // Check admin privileges
      final adminUser = await _firestore.collection('users').doc(user.uid).get();
      if (!adminUser.exists || adminUser.data()?['role'] != 'admin') {
        return {
          'success': false,
          'error': 'Only admins can send notifications',
        };
      }

      // Validate input
      if (notification.title.isEmpty || notification.body.isEmpty) {
        return {
          'success': false,
          'error': 'Title and body are required',
        };
      }

      // Store notification in Firestore first
      final docRef = await _firestore.collection('notifications').add(notification.toFirestore());

      // Get target tokens
      final tokens = await _getTargetTokens(notification);
      
      if (tokens.isEmpty) {
        await docRef.update({
          'isSent': true,
          'totalRecipients': 0,
          'successfulSends': 0,
          'failedSends': 0,
          'sentAt': FieldValue.serverTimestamp(),
        });
        
        return {
          'success': false,
          'error': 'No valid tokens found for the specified targets',
          'notificationId': docRef.id,
        };
      }

      // Send notifications in batches
      const batchSize = 100; // Smaller batch size for Flutter
      int successCount = 0;
      int failureCount = 0;
      List<String> invalidTokens = [];

      for (int i = 0; i < tokens.length; i += batchSize) {
        final batch = tokens.skip(i).take(batchSize).toList();
        
        // Process batch concurrently but with limited concurrency
        final futures = batch.map((token) => _sendFCMMessage(
          token: token,
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
          data: notification.data,
        ));

        final results = await Future.wait(futures);
        
        for (int j = 0; j < results.length; j++) {
          final result = results[j];
          if (result['success'] == true) {
            successCount++;
          } else {
            failureCount++;
            final error = result['error'].toString();
            
            // Check if token is invalid
            if (error.contains('UNREGISTERED') || 
                error.contains('INVALID_ARGUMENT') ||
                result['statusCode'] == 404) {
              invalidTokens.add(batch[j]);
            }
          }
        }
      }

      // Clean up invalid tokens
      if (invalidTokens.isNotEmpty) {
        final batch = _firestore.batch();
        for (final token in invalidTokens) {
          final tokenQuery = await _firestore
              .collection('fcm_tokens')
              .where('token', isEqualTo: token)
              .limit(1)
              .get();

          if (tokenQuery.docs.isNotEmpty) {
            batch.update(tokenQuery.docs.first.reference, {'isActive': false});
          }
        }
        await batch.commit();
      }

      // Update notification document with results
      await docRef.update({
        'isSent': true,
        'totalRecipients': tokens.length,
        'successfulSends': successCount,
        'failedSends': failureCount,
        'sentAt': FieldValue.serverTimestamp(),
        'invalidTokensRemoved': invalidTokens.length,
      });

      // Log the notification send
      await _firestore.collection('admin_logs').add({
        'action': 'notification_sent',
        'adminId': user.uid,
        'notificationId': docRef.id,
        'type': notification.type.toString().split('.').last,
        'totalRecipients': tokens.length,
        'successfulSends': successCount,
        'failedSends': failureCount,
        'timestamp': FieldValue.serverTimestamp(),
      });

      return {
        'success': true,
        'notificationId': docRef.id,
        'totalRecipients': tokens.length,
        'successfulSends': successCount,
        'failedSends': failureCount,
        'invalidTokensRemoved': invalidTokens.length,
      };

    } catch (e) {
      print('Error sending notification: $e');
      return {
        'success': false,
        'error': 'Failed to send notification: $e',
      };
    }
  }
}
