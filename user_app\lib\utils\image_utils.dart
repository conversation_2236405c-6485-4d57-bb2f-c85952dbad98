import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../services/cloudinary_service.dart';
import '../config/image_cache_config.dart';
import 'performance_monitor.dart';

/// Utility class for optimized image loading across the app
class ImageUtils {
  
  /// Build optimized CachedNetworkImage for product cards
  static Widget buildProductCardImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    final optimizedUrl = CloudinaryService.getProductCardImageUrl(imageUrl);
    final settings = ImageCacheConfig.getOptimalSettings('product_card');

    Widget imageWidget = CachedNetworkImage(
      imageUrl: optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.productImageCacheManager,
      // Android optimized settings from config
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      placeholder: (context, url) {
        PerformanceMonitor.startImageLoading(url);
        return _buildLoadingPlaceholder();
      },
      errorWidget: (context, url, error) {
        PerformanceMonitor.endImageLoading(url, success: false);
        return _buildErrorPlaceholder();
      },
      imageBuilder: (context, imageProvider) {
        PerformanceMonitor.endImageLoading(optimizedUrl, success: true);
        return Image(image: imageProvider, fit: fit);
      },
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Build optimized CachedNetworkImage for product preview
  static Widget buildProductPreviewImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    final optimizedUrl = CloudinaryService.getProductPreviewImageUrl(imageUrl);
    final settings = ImageCacheConfig.getOptimalSettings('product_preview');

    Widget imageWidget = CachedNetworkImage(
      imageUrl: optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.productImageCacheManager,
      // Android optimized settings for preview from config
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      placeholder: (context, url) {
        PerformanceMonitor.startImageLoading(url);
        return _buildPreviewLoadingPlaceholder();
      },
      errorWidget: (context, url, error) {
        PerformanceMonitor.endImageLoading(url, success: false);
        return _buildPreviewErrorPlaceholder();
      },
      imageBuilder: (context, imageProvider) {
        PerformanceMonitor.endImageLoading(optimizedUrl, success: true);
        return Image(image: imageProvider, fit: fit);
      },
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Build optimized CachedNetworkImage for general use
  static Widget buildOptimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    int? memCacheWidth,
    int? memCacheHeight,
    int? maxWidthDiskCache,
    int? maxHeightDiskCache,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: memCacheWidth ?? 400,
      memCacheHeight: memCacheHeight ?? 400,
      maxWidthDiskCache: maxWidthDiskCache ?? 800,
      maxHeightDiskCache: maxHeightDiskCache ?? 800,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      placeholder: placeholder != null 
        ? (context, url) => placeholder
        : (context, url) => _buildLoadingPlaceholder(),
      errorWidget: errorWidget != null
        ? (context, url, error) => errorWidget
        : (context, url, error) => _buildErrorPlaceholder(),
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Standard loading placeholder
  static Widget _buildLoadingPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.backgroundColor,
            AppConstants.backgroundColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppConstants.primaryColor,
          ),
        ),
      ),
    );
  }

  /// Preview loading placeholder (smaller)
  static Widget _buildPreviewLoadingPlaceholder() {
    return Container(
      color: AppConstants.backgroundColor,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: AppConstants.primaryColor,
              ),
            ),
            SizedBox(height: 4),
            Text(
              'Loading...',
              style: TextStyle(
                fontSize: 10,
                color: AppConstants.textHintColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Standard error placeholder
  static Widget _buildErrorPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.backgroundColor,
            AppConstants.backgroundColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Icon(
        Icons.image_outlined,
        size: AppConstants.iconSizeLarge,
        color: AppConstants.textHintColor,
      ),
    );
  }

  /// Preview error placeholder
  static Widget _buildPreviewErrorPlaceholder() {
    return Container(
      color: AppConstants.backgroundColor,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: AppConstants.errorColor,
            size: 24,
          ),
          SizedBox(height: 4),
          Text(
            'Failed to load',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if URL is a valid image URL
  static bool isValidImageUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             (url.toLowerCase().endsWith('.jpg') ||
              url.toLowerCase().endsWith('.jpeg') ||
              url.toLowerCase().endsWith('.png') ||
              url.toLowerCase().endsWith('.gif') ||
              url.toLowerCase().endsWith('.webp') ||
              url.toLowerCase().endsWith('.svg'));
    } catch (e) {
      return false;
    }
  }

  /// Get image file size in MB
  static Future<double> getImageSizeInMB(String imageUrl) async {
    try {
      // This is a placeholder - in a real app you might want to
      // make a HEAD request to get the content-length
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }
}
