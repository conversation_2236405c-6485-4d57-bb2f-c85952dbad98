/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace ml_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * AI Platform Training &amp; Prediction API
     *
     * An API to enable creating and using machine learning models.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const ml = google.ml('v1');
     * ```
     */
    export class Ml {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; \} service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); \} Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); \} Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.
     */
    export interface Schema$GoogleApi__HttpBody {
        /**
         * The HTTP Content-Type header value specifying the content type of the body.
         */
        contentType?: string | null;
        /**
         * The HTTP request/response body as raw binary.
         */
        data?: string | null;
        /**
         * Application specific response metadata. Must be set in the first response for streaming APIs.
         */
        extensions?: Array<{
            [key: string]: any;
        }> | null;
    }
    export interface Schema$GoogleCloudMlV1_AutomatedStoppingConfig_DecayCurveAutomatedStoppingConfig {
        /**
         * If true, measurement.elapsed_time is used as the x-axis of each Trials Decay Curve. Otherwise, Measurement.steps will be used as the x-axis.
         */
        useElapsedTime?: boolean | null;
    }
    /**
     * The median automated stopping rule stops a pending trial if the trial's best objective_value is strictly below the median 'performance' of all completed trials reported up to the trial's last measurement. Currently, 'performance' refers to the running average of the objective values reported by the trial in each measurement.
     */
    export interface Schema$GoogleCloudMlV1_AutomatedStoppingConfig_MedianAutomatedStoppingConfig {
        /**
         * If true, the median automated stopping rule applies to measurement.use_elapsed_time, which means the elapsed_time field of the current trial's latest measurement is used to compute the median objective value for each completed trial.
         */
        useElapsedTime?: boolean | null;
    }
    /**
     * An observed value of a metric.
     */
    export interface Schema$GoogleCloudMlV1_HyperparameterOutput_HyperparameterMetric {
        /**
         * The objective value at this training step.
         */
        objectiveValue?: number | null;
        /**
         * The global training step for this metric.
         */
        trainingStep?: string | null;
    }
    /**
     * A message representing a metric in the measurement.
     */
    export interface Schema$GoogleCloudMlV1_Measurement_Metric {
        /**
         * Required. Metric name.
         */
        metric?: string | null;
        /**
         * Required. The value for this metric.
         */
        value?: number | null;
    }
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_CategoricalValueSpec {
        /**
         * Must be specified if type is `CATEGORICAL`. The list of possible categories.
         */
        values?: string[] | null;
    }
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_DiscreteValueSpec {
        /**
         * Must be specified if type is `DISCRETE`. A list of feasible points. The list should be in strictly increasing order. For instance, this parameter might have possible settings of 1.5, 2.5, and 4.0. This list should not contain more than 1,000 values.
         */
        values?: number[] | null;
    }
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_DoubleValueSpec {
        /**
         * Must be specified if type is `DOUBLE`. Maximum value of the parameter.
         */
        maxValue?: number | null;
        /**
         * Must be specified if type is `DOUBLE`. Minimum value of the parameter.
         */
        minValue?: number | null;
    }
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_IntegerValueSpec {
        /**
         * Must be specified if type is `INTEGER`. Maximum value of the parameter.
         */
        maxValue?: string | null;
        /**
         * Must be specified if type is `INTEGER`. Minimum value of the parameter.
         */
        minValue?: string | null;
    }
    /**
     * Represents the spec to match categorical values from parent parameter.
     */
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentCategoricalValueSpec {
        /**
         * Matches values of the parent parameter with type 'CATEGORICAL'. All values must exist in `categorical_value_spec` of parent parameter.
         */
        values?: string[] | null;
    }
    /**
     * Represents the spec to match discrete values from parent parameter.
     */
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentDiscreteValueSpec {
        /**
         * Matches values of the parent parameter with type 'DISCRETE'. All values must exist in `discrete_value_spec` of parent parameter.
         */
        values?: number[] | null;
    }
    /**
     * Represents the spec to match integer values from parent parameter.
     */
    export interface Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentIntValueSpec {
        /**
         * Matches values of the parent parameter with type 'INTEGER'. All values must lie in `integer_value_spec` of parent parameter.
         */
        values?: string[] | null;
    }
    /**
     * Represents a metric to optimize.
     */
    export interface Schema$GoogleCloudMlV1_StudyConfig_MetricSpec {
        /**
         * Required. The optimization goal of the metric.
         */
        goal?: string | null;
        /**
         * Required. The name of the metric.
         */
        metric?: string | null;
    }
    /**
     * Represents a single parameter to optimize.
     */
    export interface Schema$GoogleCloudMlV1_StudyConfig_ParameterSpec {
        /**
         * The value spec for a 'CATEGORICAL' parameter.
         */
        categoricalValueSpec?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_CategoricalValueSpec;
        /**
         * A child node is active if the parameter's value matches the child node's matching_parent_values. If two items in child_parameter_specs have the same name, they must have disjoint matching_parent_values.
         */
        childParameterSpecs?: Schema$GoogleCloudMlV1_StudyConfig_ParameterSpec[];
        /**
         * The value spec for a 'DISCRETE' parameter.
         */
        discreteValueSpec?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_DiscreteValueSpec;
        /**
         * The value spec for a 'DOUBLE' parameter.
         */
        doubleValueSpec?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_DoubleValueSpec;
        /**
         * The value spec for an 'INTEGER' parameter.
         */
        integerValueSpec?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_IntegerValueSpec;
        /**
         * Required. The parameter name must be unique amongst all ParameterSpecs.
         */
        parameter?: string | null;
        parentCategoricalValues?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentCategoricalValueSpec;
        parentDiscreteValues?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentDiscreteValueSpec;
        parentIntValues?: Schema$GoogleCloudMlV1_StudyConfigParameterSpec_MatchingParentIntValueSpec;
        /**
         * How the parameter should be scaled. Leave unset for categorical parameters.
         */
        scaleType?: string | null;
        /**
         * Required. The type of the parameter.
         */
        type?: string | null;
    }
    /**
     * A message representing a parameter to be tuned. Contains the name of the parameter and the suggested value to use for this trial.
     */
    export interface Schema$GoogleCloudMlV1_Trial_Parameter {
        /**
         * Must be set if ParameterType is DOUBLE or DISCRETE.
         */
        floatValue?: number | null;
        /**
         * Must be set if ParameterType is INTEGER
         */
        intValue?: string | null;
        /**
         * The name of the parameter.
         */
        parameter?: string | null;
        /**
         * Must be set if ParameterTypeis CATEGORICAL
         */
        stringValue?: string | null;
    }
    /**
     * Represents a hardware accelerator request config. Note that the AcceleratorConfig can be used in both Jobs and Versions. Learn more about [accelerators for training](/ml-engine/docs/using-gpus) and [accelerators for online prediction](/ml-engine/docs/machine-types-online-prediction#gpus).
     */
    export interface Schema$GoogleCloudMlV1__AcceleratorConfig {
        /**
         * The number of accelerators to attach to each machine running the job.
         */
        count?: string | null;
        /**
         * The type of accelerator to use.
         */
        type?: string | null;
    }
    /**
     * The request message for the AddTrialMeasurement service method.
     */
    export interface Schema$GoogleCloudMlV1__AddTrialMeasurementRequest {
        /**
         * Required. The measurement to be added to a trial.
         */
        measurement?: Schema$GoogleCloudMlV1__Measurement;
    }
    /**
     * Configuration for Automated Early Stopping of Trials. If no implementation_config is set, automated early stopping will not be run.
     */
    export interface Schema$GoogleCloudMlV1__AutomatedStoppingConfig {
        decayCurveStoppingConfig?: Schema$GoogleCloudMlV1_AutomatedStoppingConfig_DecayCurveAutomatedStoppingConfig;
        medianAutomatedStoppingConfig?: Schema$GoogleCloudMlV1_AutomatedStoppingConfig_MedianAutomatedStoppingConfig;
    }
    /**
     * Options for automatically scaling a model.
     */
    export interface Schema$GoogleCloudMlV1__AutoScaling {
        /**
         * The maximum number of nodes to scale this model under load. The actual value will depend on resource quota and availability.
         */
        maxNodes?: number | null;
        /**
         * MetricSpec contains the specifications to use to calculate the desired nodes count.
         */
        metrics?: Schema$GoogleCloudMlV1__MetricSpec[];
        /**
         * Optional. The minimum number of nodes to allocate for this model. These nodes are always up, starting from the time the model is deployed. Therefore, the cost of operating this model will be at least `rate` * `min_nodes` * number of hours since last billing cycle, where `rate` is the cost per node-hour as documented in the [pricing guide](/ml-engine/docs/pricing), even if no predictions are performed. There is additional cost for each prediction performed. Unlike manual scaling, if the load gets too heavy for the nodes that are up, the service will automatically add nodes to handle the increased load as well as scale back as traffic drops, always maintaining at least `min_nodes`. You will be charged for the time in which additional nodes are used. If `min_nodes` is not specified and AutoScaling is used with a [legacy (MLS1) machine type](/ml-engine/docs/machine-types-online-prediction), `min_nodes` defaults to 0, in which case, when traffic to a model stops (and after a cool-down period), nodes will be shut down and no charges will be incurred until traffic to the model resumes. If `min_nodes` is not specified and AutoScaling is used with a [Compute Engine (N1) machine type](/ml-engine/docs/machine-types-online-prediction), `min_nodes` defaults to 1. `min_nodes` must be at least 1 for use with a Compute Engine machine type. You can set `min_nodes` when creating the model version, and you can also update `min_nodes` for an existing version: update_body.json: { 'autoScaling': { 'minNodes': 5 \} \} HTTP request: PATCH https://ml.googleapis.com/v1/{name=projects/x/models/x/versions/x\}?update_mask=autoScaling.minNodes -d @./update_body.json
         */
        minNodes?: number | null;
    }
    /**
     * Represents output related to a built-in algorithm Job.
     */
    export interface Schema$GoogleCloudMlV1__BuiltInAlgorithmOutput {
        /**
         * Framework on which the built-in algorithm was trained.
         */
        framework?: string | null;
        /**
         * The Cloud Storage path to the `model/` directory where the training job saves the trained model. Only set for successful jobs that don't use hyperparameter tuning.
         */
        modelPath?: string | null;
        /**
         * Python version on which the built-in algorithm was trained.
         */
        pythonVersion?: string | null;
        /**
         * AI Platform runtime version on which the built-in algorithm was trained.
         */
        runtimeVersion?: string | null;
    }
    /**
     * Request message for the CancelJob method.
     */
    export interface Schema$GoogleCloudMlV1__CancelJobRequest {
    }
    export interface Schema$GoogleCloudMlV1__Capability {
        /**
         * Available accelerators for the capability.
         */
        availableAccelerators?: string[] | null;
        type?: string | null;
    }
    /**
     * This message will be placed in the metadata field of a google.longrunning.Operation associated with a CheckTrialEarlyStoppingState request.
     */
    export interface Schema$GoogleCloudMlV1__CheckTrialEarlyStoppingStateMetatdata {
        /**
         * The time at which the operation was submitted.
         */
        createTime?: string | null;
        /**
         * The name of the study that the trial belongs to.
         */
        study?: string | null;
        /**
         * The trial name.
         */
        trial?: string | null;
    }
    /**
     * The request message for the CheckTrialEarlyStoppingState service method.
     */
    export interface Schema$GoogleCloudMlV1__CheckTrialEarlyStoppingStateRequest {
    }
    /**
     * The message will be placed in the response field of a completed google.longrunning.Operation associated with a CheckTrialEarlyStoppingState request.
     */
    export interface Schema$GoogleCloudMlV1__CheckTrialEarlyStoppingStateResponse {
        /**
         * The time at which operation processing completed.
         */
        endTime?: string | null;
        /**
         * True if the Trial should stop.
         */
        shouldStop?: boolean | null;
        /**
         * The time at which the operation was started.
         */
        startTime?: string | null;
    }
    /**
     * The request message for the CompleteTrial service method.
     */
    export interface Schema$GoogleCloudMlV1__CompleteTrialRequest {
        /**
         * Optional. If provided, it will be used as the completed trial's final_measurement; Otherwise, the service will auto-select a previously reported measurement as the final-measurement
         */
        finalMeasurement?: Schema$GoogleCloudMlV1__Measurement;
        /**
         * Optional. A human readable reason why the trial was infeasible. This should only be provided if `trial_infeasible` is true.
         */
        infeasibleReason?: string | null;
        /**
         * Optional. True if the trial cannot be run with the given Parameter, and final_measurement will be ignored.
         */
        trialInfeasible?: boolean | null;
    }
    export interface Schema$GoogleCloudMlV1__Config {
        /**
         * The service account Cloud ML uses to run on TPU node.
         */
        tpuServiceAccount?: string | null;
    }
    /**
     * Represents a network port in a single container. This message is a subset of the [Kubernetes ContainerPort v1 core specification](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#containerport-v1-core).
     */
    export interface Schema$GoogleCloudMlV1__ContainerPort {
        /**
         * Number of the port to expose on the container. This must be a valid port number: 0 < PORT_NUMBER < 65536.
         */
        containerPort?: number | null;
    }
    /**
     * Specification of a custom container for serving predictions. This message is a subset of the [Kubernetes Container v1 core specification](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#container-v1-core).
     */
    export interface Schema$GoogleCloudMlV1__ContainerSpec {
        /**
         * Immutable. Specifies arguments for the command that runs when the container starts. This overrides the container's [`CMD`](https://docs.docker.com/engine/reference/builder/#cmd). Specify this field as an array of executable and arguments, similar to a Docker `CMD`'s "default parameters" form. If you don't specify this field but do specify the command field, then the command from the `command` field runs without any additional arguments. See the [Kubernetes documentation about how the `command` and `args` fields interact with a container's `ENTRYPOINT` and `CMD`](https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#notes). If you don't specify this field and don't specify the `commmand` field, then the container's [`ENTRYPOINT`](https://docs.docker.com/engine/reference/builder/#cmd) and `CMD` determine what runs based on their default behavior. See the [Docker documentation about how `CMD` and `ENTRYPOINT` interact](https://docs.docker.com/engine/reference/builder/#understand-how-cmd-and-entrypoint-interact). In this field, you can reference [environment variables set by AI Platform Prediction](/ai-platform/prediction/docs/custom-container-requirements#aip-variables) and environment variables set in the env field. You cannot reference environment variables set in the Docker image. In order for environment variables to be expanded, reference them by using the following syntax: $( VARIABLE_NAME) Note that this differs from Bash variable expansion, which does not use parentheses. If a variable cannot be resolved, the reference in the input string is used unchanged. To avoid variable expansion, you can escape this syntax with `$$`; for example: $$(VARIABLE_NAME) This field corresponds to the `args` field of the [Kubernetes Containers v1 core API](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#container-v1-core).
         */
        args?: string[] | null;
        /**
         * Immutable. Specifies the command that runs when the container starts. This overrides the container's [`ENTRYPOINT`](https://docs.docker.com/engine/reference/builder/#entrypoint). Specify this field as an array of executable and arguments, similar to a Docker `ENTRYPOINT`'s "exec" form, not its "shell" form. If you do not specify this field, then the container's `ENTRYPOINT` runs, in conjunction with the args field or the container's [`CMD`](https://docs.docker.com/engine/reference/builder/#cmd), if either exists. If this field is not specified and the container does not have an `ENTRYPOINT`, then refer to the [Docker documentation about how `CMD` and `ENTRYPOINT` interact](https://docs.docker.com/engine/reference/builder/#understand-how-cmd-and-entrypoint-interact). If you specify this field, then you can also specify the `args` field to provide additional arguments for this command. However, if you specify this field, then the container's `CMD` is ignored. See the [Kubernetes documentation about how the `command` and `args` fields interact with a container's `ENTRYPOINT` and `CMD`](https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#notes). In this field, you can reference [environment variables set by AI Platform Prediction](/ai-platform/prediction/docs/custom-container-requirements#aip-variables) and environment variables set in the env field. You cannot reference environment variables set in the Docker image. In order for environment variables to be expanded, reference them by using the following syntax: $( VARIABLE_NAME) Note that this differs from Bash variable expansion, which does not use parentheses. If a variable cannot be resolved, the reference in the input string is used unchanged. To avoid variable expansion, you can escape this syntax with `$$`; for example: $$(VARIABLE_NAME) This field corresponds to the `command` field of the [Kubernetes Containers v1 core API](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#container-v1-core).
         */
        command?: string[] | null;
        /**
         * Immutable. List of environment variables to set in the container. After the container starts running, code running in the container can read these environment variables. Additionally, the command and args fields can reference these variables. Later entries in this list can also reference earlier entries. For example, the following example sets the variable `VAR_2` to have the value `foo bar`: ```json [ { "name": "VAR_1", "value": "foo" \}, { "name": "VAR_2", "value": "$(VAR_1) bar" \} ] ``` If you switch the order of the variables in the example, then the expansion does not occur. This field corresponds to the `env` field of the [Kubernetes Containers v1 core API](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#container-v1-core).
         */
        env?: Schema$GoogleCloudMlV1__EnvVar[];
        /**
         * URI of the Docker image to be used as the custom container for serving predictions. This URI must identify [an image in Artifact Registry](/artifact-registry/docs/overview) and begin with the hostname `{REGION\}-docker.pkg.dev`, where `{REGION\}` is replaced by the region that matches AI Platform Prediction [regional endpoint](/ai-platform/prediction/docs/regional-endpoints) that you are using. For example, if you are using the `us-central1-ml.googleapis.com` endpoint, then this URI must begin with `us-central1-docker.pkg.dev`. To use a custom container, the [AI Platform Google-managed service account](/ai-platform/prediction/docs/custom-service-account#default) must have permission to pull (read) the Docker image at this URI. The AI Platform Google-managed service account has the following format: `service-{PROJECT_NUMBER\}@cloud-ml.google.com.iam.gserviceaccount.com` {PROJECT_NUMBER\} is replaced by your Google Cloud project number. By default, this service account has necessary permissions to pull an Artifact Registry image in the same Google Cloud project where you are using AI Platform Prediction. In this case, no configuration is necessary. If you want to use an image from a different Google Cloud project, learn how to [grant the Artifact Registry Reader (roles/artifactregistry.reader) role for a repository](/artifact-registry/docs/access-control#grant-repo) to your projet's AI Platform Google-managed service account. To learn about the requirements for the Docker image itself, read [Custom container requirements](/ai-platform/prediction/docs/custom-container-requirements).
         */
        image?: string | null;
        /**
         * Immutable. List of ports to expose from the container. AI Platform Prediction sends any prediction requests that it receives to the first port on this list. AI Platform Prediction also sends [liveness and health checks](/ai-platform/prediction/docs/custom-container-requirements#health) to this port. If you do not specify this field, it defaults to following value: ```json [ { "containerPort": 8080 \} ] ``` AI Platform Prediction does not use ports other than the first one listed. This field corresponds to the `ports` field of the [Kubernetes Containers v1 core API](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#container-v1-core).
         */
        ports?: Schema$GoogleCloudMlV1__ContainerPort[];
    }
    /**
     * Represents the config of disk options.
     */
    export interface Schema$GoogleCloudMlV1__DiskConfig {
        /**
         * Size in GB of the boot disk (default is 100GB).
         */
        bootDiskSizeGb?: number | null;
        /**
         * Type of the boot disk (default is "pd-ssd"). Valid values: "pd-ssd" (Persistent Disk Solid State Drive) or "pd-standard" (Persistent Disk Hard Disk Drive).
         */
        bootDiskType?: string | null;
    }
    /**
     * Represents a custom encryption key configuration that can be applied to a resource.
     */
    export interface Schema$GoogleCloudMlV1__EncryptionConfig {
        /**
         * The Cloud KMS resource identifier of the customer-managed encryption key used to protect a resource, such as a training job. It has the following format: `projects/{PROJECT_ID\}/locations/{REGION\}/keyRings/{KEY_RING_NAME\}/cryptoKeys/{KEY_NAME\}`
         */
        kmsKeyName?: string | null;
    }
    /**
     * Represents an environment variable to be made available in a container. This message is a subset of the [Kubernetes EnvVar v1 core specification](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#envvar-v1-core).
     */
    export interface Schema$GoogleCloudMlV1__EnvVar {
        /**
         * Name of the environment variable. Must be a [valid C identifier](https://github.com/kubernetes/kubernetes/blob/v1.18.8/staging/src/k8s.io/apimachinery/pkg/util/validation/validation.go#L258) and must not begin with the prefix `AIP_`.
         */
        name?: string | null;
        /**
         * Value of the environment variable. Defaults to an empty string. In this field, you can reference [environment variables set by AI Platform Prediction](/ai-platform/prediction/docs/custom-container-requirements#aip-variables) and environment variables set earlier in the same env field as where this message occurs. You cannot reference environment variables set in the Docker image. In order for environment variables to be expanded, reference them by using the following syntax: $(VARIABLE_NAME) Note that this differs from Bash variable expansion, which does not use parentheses. If a variable cannot be resolved, the reference in the input string is used unchanged. To avoid variable expansion, you can escape this syntax with `$$`; for example: $$(VARIABLE_NAME)
         */
        value?: string | null;
    }
    /**
     * Request for explanations to be issued against a trained model.
     */
    export interface Schema$GoogleCloudMlV1__ExplainRequest {
        /**
         * Required. The explanation request body.
         */
        httpBody?: Schema$GoogleApi__HttpBody;
    }
    /**
     * Message holding configuration options for explaining model predictions. There are three feature attribution methods supported for TensorFlow models: integrated gradients, sampled Shapley, and XRAI. [Learn more about feature attributions.](/ai-platform/prediction/docs/ai-explanations/overview)
     */
    export interface Schema$GoogleCloudMlV1__ExplanationConfig {
        /**
         * Attributes credit by computing the Aumann-Shapley value taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1703.01365
         */
        integratedGradientsAttribution?: Schema$GoogleCloudMlV1__IntegratedGradientsAttribution;
        /**
         * An attribution method that approximates Shapley values for features that contribute to the label being predicted. A sampling strategy is used to approximate the value rather than considering all subsets of features.
         */
        sampledShapleyAttribution?: Schema$GoogleCloudMlV1__SampledShapleyAttribution;
        /**
         * Attributes credit by computing the XRAI taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1906.02825 Currently only implemented for models with natural image inputs.
         */
        xraiAttribution?: Schema$GoogleCloudMlV1__XraiAttribution;
    }
    /**
     * Returns service account information associated with a project.
     */
    export interface Schema$GoogleCloudMlV1__GetConfigResponse {
        config?: Schema$GoogleCloudMlV1__Config;
        /**
         * The service account Cloud ML uses to access resources in the project.
         */
        serviceAccount?: string | null;
        /**
         * The project number for `service_account`.
         */
        serviceAccountProject?: string | null;
    }
    /**
     * Represents the result of a single hyperparameter tuning trial from a training job. The TrainingOutput object that is returned on successful completion of a training job with hyperparameter tuning includes a list of HyperparameterOutput objects, one for each successful trial.
     */
    export interface Schema$GoogleCloudMlV1__HyperparameterOutput {
        /**
         * All recorded object metrics for this trial. This field is not currently populated.
         */
        allMetrics?: Schema$GoogleCloudMlV1_HyperparameterOutput_HyperparameterMetric[];
        /**
         * Details related to built-in algorithms jobs. Only set for trials of built-in algorithms jobs that have succeeded.
         */
        builtInAlgorithmOutput?: Schema$GoogleCloudMlV1__BuiltInAlgorithmOutput;
        /**
         * Output only. End time for the trial.
         */
        endTime?: string | null;
        /**
         * The final objective metric seen for this trial.
         */
        finalMetric?: Schema$GoogleCloudMlV1_HyperparameterOutput_HyperparameterMetric;
        /**
         * The hyperparameters given to this trial.
         */
        hyperparameters?: {
            [key: string]: string;
        } | null;
        /**
         * True if the trial is stopped early.
         */
        isTrialStoppedEarly?: boolean | null;
        /**
         * Output only. Start time for the trial.
         */
        startTime?: string | null;
        /**
         * Output only. The detailed state of the trial.
         */
        state?: string | null;
        /**
         * The trial id for these results.
         */
        trialId?: string | null;
        /**
         * URIs for accessing [interactive shells](https://cloud.google.com/ai-platform/training/docs/monitor-debug-interactive-shell) (one URI for each training node). Only available if this trial is part of a hyperparameter tuning job and the job's training_input.enable_web_access is `true`. The keys are names of each node in the training job; for example, `master-replica-0` for the master node, `worker-replica-0` for the first worker, and `ps-replica-0` for the first parameter server. The values are the URIs for each node's interactive shell.
         */
        webAccessUris?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Represents a set of hyperparameters to optimize.
     */
    export interface Schema$GoogleCloudMlV1__HyperparameterSpec {
        /**
         * Optional. The search algorithm specified for the hyperparameter tuning job. Uses the default AI Platform hyperparameter tuning algorithm if unspecified.
         */
        algorithm?: string | null;
        /**
         * Optional. Indicates if the hyperparameter tuning job enables auto trial early stopping.
         */
        enableTrialEarlyStopping?: boolean | null;
        /**
         * Required. The type of goal to use for tuning. Available types are `MAXIMIZE` and `MINIMIZE`. Defaults to `MAXIMIZE`.
         */
        goal?: string | null;
        /**
         * Optional. The TensorFlow summary tag name to use for optimizing trials. For current versions of TensorFlow, this tag name should exactly match what is shown in TensorBoard, including all scopes. For versions of TensorFlow prior to 0.12, this should be only the tag passed to tf.Summary. By default, "training/hptuning/metric" will be used.
         */
        hyperparameterMetricTag?: string | null;
        /**
         * Optional. The number of failed trials that need to be seen before failing the hyperparameter tuning job. You can specify this field to override the default failing criteria for AI Platform hyperparameter tuning jobs. Defaults to zero, which means the service decides when a hyperparameter job should fail.
         */
        maxFailedTrials?: number | null;
        /**
         * Optional. The number of training trials to run concurrently. You can reduce the time it takes to perform hyperparameter tuning by adding trials in parallel. However, each trail only benefits from the information gained in completed trials. That means that a trial does not get access to the results of trials running at the same time, which could reduce the quality of the overall optimization. Each trial will use the same scale tier and machine types. Defaults to one.
         */
        maxParallelTrials?: number | null;
        /**
         * Optional. How many training trials should be attempted to optimize the specified hyperparameters. Defaults to one.
         */
        maxTrials?: number | null;
        /**
         * Required. The set of parameters to tune.
         */
        params?: Schema$GoogleCloudMlV1__ParameterSpec[];
        /**
         * Optional. The prior hyperparameter tuning job id that users hope to continue with. The job id will be used to find the corresponding vizier study guid and resume the study.
         */
        resumePreviousJobId?: string | null;
    }
    /**
     * Attributes credit by computing the Aumann-Shapley value taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1703.01365
     */
    export interface Schema$GoogleCloudMlV1__IntegratedGradientsAttribution {
        /**
         * Number of steps for approximating the path integral. A good value to start is 50 and gradually increase until the sum to diff property is met within the desired error range.
         */
        numIntegralSteps?: number | null;
    }
    /**
     * Represents a training or prediction job.
     */
    export interface Schema$GoogleCloudMlV1__Job {
        /**
         * Output only. When the job was created.
         */
        createTime?: string | null;
        /**
         * Output only. When the job processing was completed.
         */
        endTime?: string | null;
        /**
         * Output only. The details of a failure or a cancellation.
         */
        errorMessage?: string | null;
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a job from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform job updates in order to avoid race conditions: An `etag` is returned in the response to `GetJob`, and systems are expected to put that etag in the request to `UpdateJob` to ensure that their change will be applied to the same version of the job.
         */
        etag?: string | null;
        /**
         * Required. The user-specified id of the job.
         */
        jobId?: string | null;
        /**
         * Output only. It's only effect when the job is in QUEUED state. If it's positive, it indicates the job's position in the job scheduler. It's 0 when the job is already scheduled.
         */
        jobPosition?: string | null;
        /**
         * Optional. One or more labels that you can add, to organize your jobs. Each label is a key-value pair, where both the key and the value are arbitrary strings that you supply. For more information, see the documentation on using labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Input parameters to create a prediction job.
         */
        predictionInput?: Schema$GoogleCloudMlV1__PredictionInput;
        /**
         * The current prediction job result.
         */
        predictionOutput?: Schema$GoogleCloudMlV1__PredictionOutput;
        /**
         * Output only. When the job processing was started.
         */
        startTime?: string | null;
        /**
         * Output only. The detailed state of a job.
         */
        state?: string | null;
        /**
         * Input parameters to create a training job.
         */
        trainingInput?: Schema$GoogleCloudMlV1__TrainingInput;
        /**
         * The current training job result.
         */
        trainingOutput?: Schema$GoogleCloudMlV1__TrainingOutput;
    }
    /**
     * Response message for the ListJobs method.
     */
    export interface Schema$GoogleCloudMlV1__ListJobsResponse {
        /**
         * The list of jobs.
         */
        jobs?: Schema$GoogleCloudMlV1__Job[];
        /**
         * Optional. Pass this token as the `page_token` field of the request for a subsequent call.
         */
        nextPageToken?: string | null;
    }
    export interface Schema$GoogleCloudMlV1__ListLocationsResponse {
        /**
         * Locations where at least one type of CMLE capability is available.
         */
        locations?: Schema$GoogleCloudMlV1__Location[];
        /**
         * Optional. Pass this token as the `page_token` field of the request for a subsequent call.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for the ListModels method.
     */
    export interface Schema$GoogleCloudMlV1__ListModelsResponse {
        /**
         * The list of models.
         */
        models?: Schema$GoogleCloudMlV1__Model[];
        /**
         * Optional. Pass this token as the `page_token` field of the request for a subsequent call.
         */
        nextPageToken?: string | null;
    }
    /**
     * The request message for the ListTrials service method.
     */
    export interface Schema$GoogleCloudMlV1__ListOptimalTrialsRequest {
    }
    /**
     * The response message for the ListOptimalTrials method.
     */
    export interface Schema$GoogleCloudMlV1__ListOptimalTrialsResponse {
        /**
         * The pareto-optimal trials for multiple objective study or the optimal trial for single objective study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency
         */
        trials?: Schema$GoogleCloudMlV1__Trial[];
    }
    export interface Schema$GoogleCloudMlV1__ListStudiesResponse {
        /**
         * The studies associated with the project.
         */
        studies?: Schema$GoogleCloudMlV1__Study[];
    }
    /**
     * The response message for the ListTrials method.
     */
    export interface Schema$GoogleCloudMlV1__ListTrialsResponse {
        /**
         * The trials associated with the study.
         */
        trials?: Schema$GoogleCloudMlV1__Trial[];
    }
    /**
     * Response message for the ListVersions method.
     */
    export interface Schema$GoogleCloudMlV1__ListVersionsResponse {
        /**
         * Optional. Pass this token as the `page_token` field of the request for a subsequent call.
         */
        nextPageToken?: string | null;
        /**
         * The list of versions.
         */
        versions?: Schema$GoogleCloudMlV1__Version[];
    }
    export interface Schema$GoogleCloudMlV1__Location {
        /**
         * Capabilities available in the location.
         */
        capabilities?: Schema$GoogleCloudMlV1__Capability[];
        name?: string | null;
    }
    /**
     * Options for manually scaling a model.
     */
    export interface Schema$GoogleCloudMlV1__ManualScaling {
        /**
         * The number of nodes to allocate for this model. These nodes are always up, starting from the time the model is deployed, so the cost of operating this model will be proportional to `nodes` * number of hours since last billing cycle plus the cost for each prediction performed.
         */
        nodes?: number | null;
    }
    /**
     * A message representing a measurement.
     */
    export interface Schema$GoogleCloudMlV1__Measurement {
        /**
         * Output only. Time that the trial has been running at the point of this measurement.
         */
        elapsedTime?: string | null;
        /**
         * Provides a list of metrics that act as inputs into the objective function.
         */
        metrics?: Schema$GoogleCloudMlV1_Measurement_Metric[];
        /**
         * The number of steps a machine learning model has been trained for. Must be non-negative.
         */
        stepCount?: string | null;
    }
    /**
     * MetricSpec contains the specifications to use to calculate the desired nodes count when autoscaling is enabled.
     */
    export interface Schema$GoogleCloudMlV1__MetricSpec {
        /**
         * metric name.
         */
        name?: string | null;
        /**
         * Target specifies the target value for the given metric; once real metric deviates from the threshold by a certain percentage, the node count changes.
         */
        target?: number | null;
    }
    /**
     * Represents a machine learning solution. A model can have multiple versions, each of which is a deployed, trained model ready to receive prediction requests. The model itself is just a container.
     */
    export interface Schema$GoogleCloudMlV1__Model {
        /**
         * Output only. The default version of the model. This version will be used to handle prediction requests that do not specify a version. You can change the default version by calling projects.models.versions.setDefault.
         */
        defaultVersion?: Schema$GoogleCloudMlV1__Version;
        /**
         * Optional. The description specified for the model when it was created.
         */
        description?: string | null;
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a model from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform model updates in order to avoid race conditions: An `etag` is returned in the response to `GetModel`, and systems are expected to put that etag in the request to `UpdateModel` to ensure that their change will be applied to the model as intended.
         */
        etag?: string | null;
        /**
         * Optional. One or more labels that you can add, to organize your models. Each label is a key-value pair, where both the key and the value are arbitrary strings that you supply. For more information, see the documentation on using labels. Note that this field is not updatable for mls1* models.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. The name specified for the model when it was created. The model name must be unique within the project it is created in.
         */
        name?: string | null;
        /**
         * Optional. If true, online prediction nodes send `stderr` and `stdout` streams to Cloud Logging. These can be more verbose than the standard access logs (see `onlinePredictionLogging`) and can incur higher cost. However, they are helpful for debugging. Note that [logs may incur a cost](/stackdriver/pricing), especially if your project receives prediction requests at a high QPS. Estimate your costs before enabling this option. Default is false.
         */
        onlinePredictionConsoleLogging?: boolean | null;
        /**
         * Optional. If true, online prediction access logs are sent to Cloud Logging. These logs are like standard server access logs, containing information like timestamp and latency for each request. Note that [logs may incur a cost](/stackdriver/pricing), especially if your project receives prediction requests at a high queries per second rate (QPS). Estimate your costs before enabling this option. Default is false.
         */
        onlinePredictionLogging?: boolean | null;
        /**
         * Optional. The list of regions where the model is going to be deployed. Only one region per model is supported. Defaults to 'us-central1' if nothing is set. See the available regions for AI Platform services. Note: * No matter where a model is deployed, it can always be accessed by users from anywhere, both for online and batch prediction. * The region for a batch prediction job is set by the region field when submitting the batch prediction job and does not take its value from this field.
         */
        regions?: string[] | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudMlV1__OperationMetadata {
        /**
         * The time the operation was submitted.
         */
        createTime?: string | null;
        /**
         * The time operation processing completed.
         */
        endTime?: string | null;
        /**
         * Indicates whether a request to cancel this operation has been made.
         */
        isCancellationRequested?: boolean | null;
        /**
         * The user labels, inherited from the model or the model version being operated on.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Contains the name of the model associated with the operation.
         */
        modelName?: string | null;
        /**
         * The operation type.
         */
        operationType?: string | null;
        /**
         * Contains the project number associated with the operation.
         */
        projectNumber?: string | null;
        /**
         * The time operation processing started.
         */
        startTime?: string | null;
        /**
         * Contains the version associated with the operation.
         */
        version?: Schema$GoogleCloudMlV1__Version;
    }
    /**
     * Represents a single hyperparameter to optimize.
     */
    export interface Schema$GoogleCloudMlV1__ParameterSpec {
        /**
         * Required if type is `CATEGORICAL`. The list of possible categories.
         */
        categoricalValues?: string[] | null;
        /**
         * Required if type is `DISCRETE`. A list of feasible points. The list should be in strictly increasing order. For instance, this parameter might have possible settings of 1.5, 2.5, and 4.0. This list should not contain more than 1,000 values.
         */
        discreteValues?: number[] | null;
        /**
         * Required if type is `DOUBLE` or `INTEGER`. This field should be unset if type is `CATEGORICAL`. This value should be integers if type is `INTEGER`.
         */
        maxValue?: number | null;
        /**
         * Required if type is `DOUBLE` or `INTEGER`. This field should be unset if type is `CATEGORICAL`. This value should be integers if type is INTEGER.
         */
        minValue?: number | null;
        /**
         * Required. The parameter name must be unique amongst all ParameterConfigs in a HyperparameterSpec message. E.g., "learning_rate".
         */
        parameterName?: string | null;
        /**
         * Optional. How the parameter should be scaled to the hypercube. Leave unset for categorical parameters. Some kind of scaling is strongly recommended for real or integral parameters (e.g., `UNIT_LINEAR_SCALE`).
         */
        scaleType?: string | null;
        /**
         * Required. The type of the parameter.
         */
        type?: string | null;
    }
    /**
     * Represents input parameters for a prediction job.
     */
    export interface Schema$GoogleCloudMlV1__PredictionInput {
        /**
         * Optional. Number of records per batch, defaults to 64. The service will buffer batch_size number of records in memory before invoking one Tensorflow prediction call internally. So take the record size and memory available into consideration when setting this parameter.
         */
        batchSize?: string | null;
        /**
         * Required. The format of the input data files.
         */
        dataFormat?: string | null;
        /**
         * Required. The Cloud Storage location of the input data files. May contain wildcards.
         */
        inputPaths?: string[] | null;
        /**
         * Optional. The maximum number of workers to be used for parallel processing. Defaults to 10 if not specified.
         */
        maxWorkerCount?: string | null;
        /**
         * Use this field if you want to use the default version for the specified model. The string must use the following format: `"projects/YOUR_PROJECT/models/YOUR_MODEL"`
         */
        modelName?: string | null;
        /**
         * Optional. Format of the output data files, defaults to JSON.
         */
        outputDataFormat?: string | null;
        /**
         * Required. The output Google Cloud Storage location.
         */
        outputPath?: string | null;
        /**
         * Required. The Google Compute Engine region to run the prediction job in. See the available regions for AI Platform services.
         */
        region?: string | null;
        /**
         * Optional. The AI Platform runtime version to use for this batch prediction. If not set, AI Platform will pick the runtime version used during the CreateVersion request for this model version, or choose the latest stable version when model version information is not available such as when the model is specified by uri.
         */
        runtimeVersion?: string | null;
        /**
         * Optional. The name of the signature defined in the SavedModel to use for this job. Please refer to [SavedModel](https://tensorflow.github.io/serving/serving_basic.html) for information about how to use signatures. Defaults to [DEFAULT_SERVING_SIGNATURE_DEF_KEY](https://www.tensorflow.org/api_docs/python/tf/saved_model/signature_constants) , which is "serving_default".
         */
        signatureName?: string | null;
        /**
         * Use this field if you want to specify a Google Cloud Storage path for the model to use.
         */
        uri?: string | null;
        /**
         * Use this field if you want to specify a version of the model to use. The string is formatted the same way as `model_version`, with the addition of the version information: `"projects/YOUR_PROJECT/models/YOUR_MODEL/versions/YOUR_VERSION"`
         */
        versionName?: string | null;
    }
    /**
     * Represents results of a prediction job.
     */
    export interface Schema$GoogleCloudMlV1__PredictionOutput {
        /**
         * The number of data instances which resulted in errors.
         */
        errorCount?: string | null;
        /**
         * Node hours used by the batch prediction job.
         */
        nodeHours?: number | null;
        /**
         * The output Google Cloud Storage location provided at the job creation time.
         */
        outputPath?: string | null;
        /**
         * The number of generated predictions.
         */
        predictionCount?: string | null;
    }
    /**
     * Request for predictions to be issued against a trained model.
     */
    export interface Schema$GoogleCloudMlV1__PredictRequest {
        /**
         *  Required. The prediction request body. Refer to the [request body details section](#request-body-details) for more information on how to structure your request.
         */
        httpBody?: Schema$GoogleApi__HttpBody;
    }
    /**
     * Represents the configuration for a replica in a cluster.
     */
    export interface Schema$GoogleCloudMlV1__ReplicaConfig {
        /**
         * Represents the type and number of accelerators used by the replica. [Learn about restrictions on accelerator configurations for training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu)
         */
        acceleratorConfig?: Schema$GoogleCloudMlV1__AcceleratorConfig;
        /**
         * Arguments to the entrypoint command. The following rules apply for container_command and container_args: - If you do not supply command or args: The defaults defined in the Docker image are used. - If you supply a command but no args: The default EntryPoint and the default Cmd defined in the Docker image are ignored. Your command is run without any arguments. - If you supply only args: The default Entrypoint defined in the Docker image is run with the args that you supplied. - If you supply a command and args: The default Entrypoint and the default Cmd defined in the Docker image are ignored. Your command is run with your args. It cannot be set if custom container image is not provided. Note that this field and [TrainingInput.args] are mutually exclusive, i.e., both cannot be set at the same time.
         */
        containerArgs?: string[] | null;
        /**
         * The command with which the replica's custom container is run. If provided, it will override default ENTRYPOINT of the docker image. If not provided, the docker image's ENTRYPOINT is used. It cannot be set if custom container image is not provided. Note that this field and [TrainingInput.args] are mutually exclusive, i.e., both cannot be set at the same time.
         */
        containerCommand?: string[] | null;
        /**
         * Represents the configuration of disk options.
         */
        diskConfig?: Schema$GoogleCloudMlV1__DiskConfig;
        /**
         * The Docker image to run on the replica. This image must be in Container Registry. Learn more about [configuring custom containers](/ai-platform/training/docs/distributed-training-containers).
         */
        imageUri?: string | null;
        /**
         * The AI Platform runtime version that includes a TensorFlow version matching the one used in the custom container. This field is required if the replica is a TPU worker that uses a custom container. Otherwise, do not specify this field. This must be a [runtime version that currently supports training with TPUs](/ml-engine/docs/tensorflow/runtime-version-list#tpu-support). Note that the version of TensorFlow included in a runtime version may differ from the numbering of the runtime version itself, because it may have a different [patch version](https://www.tensorflow.org/guide/version_compat#semantic_versioning_20). In this field, you must specify the runtime version (TensorFlow minor version). For example, if your custom container runs TensorFlow `1.x.y`, specify `1.x`.
         */
        tpuTfVersion?: string | null;
    }
    /**
     * Configuration for logging request-response pairs to a BigQuery table. Online prediction requests to a model version and the responses to these requests are converted to raw strings and saved to the specified BigQuery table. Logging is constrained by [BigQuery quotas and limits](/bigquery/quotas). If your project exceeds BigQuery quotas or limits, AI Platform Prediction does not log request-response pairs, but it continues to serve predictions. If you are using [continuous evaluation](/ml-engine/docs/continuous-evaluation/), you do not need to specify this configuration manually. Setting up continuous evaluation automatically enables logging of request-response pairs.
     */
    export interface Schema$GoogleCloudMlV1__RequestLoggingConfig {
        /**
         * Required. Fully qualified BigQuery table name in the following format: " project_id.dataset_name.table_name" The specified table must already exist, and the "Cloud ML Service Agent" for your project must have permission to write to it. The table must have the following [schema](/bigquery/docs/schemas): Field nameType Mode model STRING REQUIRED model_version STRING REQUIRED time TIMESTAMP REQUIRED raw_data STRING REQUIRED raw_prediction STRING NULLABLE groundtruth STRING NULLABLE
         */
        bigqueryTableName?: string | null;
        /**
         * Percentage of requests to be logged, expressed as a fraction from 0 to 1. For example, if you want to log 10% of requests, enter `0.1`. The sampling window is the lifetime of the model version. Defaults to 0.
         */
        samplingPercentage?: number | null;
    }
    /**
     * Specifies HTTP paths served by a custom container. AI Platform Prediction sends requests to these paths on the container; the custom container must run an HTTP server that responds to these requests with appropriate responses. Read [Custom container requirements](/ai-platform/prediction/docs/custom-container-requirements) for details on how to create your container image to meet these requirements.
     */
    export interface Schema$GoogleCloudMlV1__RouteMap {
        /**
         * HTTP path on the container to send health checkss to. AI Platform Prediction intermittently sends GET requests to this path on the container's IP address and port to check that the container is healthy. Read more about [health checks](/ai-platform/prediction/docs/custom-container-requirements#checks). For example, if you set this field to `/bar`, then AI Platform Prediction intermittently sends a GET request to the `/bar` path on the port of your container specified by the first value of Version.container.ports. If you don't specify this field, it defaults to the following value: /v1/models/ MODEL/versions/VERSION The placeholders in this value are replaced as follows: * MODEL: The name of the parent Model. This does not include the "projects/PROJECT_ID/models/" prefix that the API returns in output; it is the bare model name, as provided to projects.models.create. * VERSION: The name of the model version. This does not include the "projects/PROJECT_ID /models/MODEL/versions/" prefix that the API returns in output; it is the bare version name, as provided to projects.models.versions.create.
         */
        health?: string | null;
        /**
         * HTTP path on the container to send prediction requests to. AI Platform Prediction forwards requests sent using projects.predict to this path on the container's IP address and port. AI Platform Prediction then returns the container's response in the API response. For example, if you set this field to `/foo`, then when AI Platform Prediction receives a prediction request, it forwards the request body in a POST request to the `/foo` path on the port of your container specified by the first value of Version.container.ports. If you don't specify this field, it defaults to the following value: /v1/models/MODEL/versions/VERSION:predict The placeholders in this value are replaced as follows: * MODEL: The name of the parent Model. This does not include the "projects/PROJECT_ID/models/" prefix that the API returns in output; it is the bare model name, as provided to projects.models.create. * VERSION: The name of the model version. This does not include the "projects/PROJECT_ID/models/MODEL/versions/" prefix that the API returns in output; it is the bare version name, as provided to projects.models.versions.create.
         */
        predict?: string | null;
    }
    /**
     * An attribution method that approximates Shapley values for features that contribute to the label being predicted. A sampling strategy is used to approximate the value rather than considering all subsets of features.
     */
    export interface Schema$GoogleCloudMlV1__SampledShapleyAttribution {
        /**
         * The number of feature permutations to consider when approximating the Shapley values.
         */
        numPaths?: number | null;
    }
    /**
     * All parameters related to scheduling of training jobs.
     */
    export interface Schema$GoogleCloudMlV1__Scheduling {
        /**
         * Optional. The maximum job running time, expressed in seconds. The field can contain up to nine fractional digits, terminated by `s`. If not specified, this field defaults to `604800s` (seven days). If the training job is still running after this duration, AI Platform Training cancels it. The duration is measured from when the job enters the `RUNNING` state; therefore it does not overlap with the duration limited by Scheduling.max_wait_time. For example, if you want to ensure your job runs for no more than 2 hours, set this field to `7200s` (2 hours * 60 minutes / hour * 60 seconds / minute). If you submit your training job using the `gcloud` tool, you can [specify this field in a `config.yaml` file](/ai-platform/training/docs/training-jobs#formatting_your_configuration_parameters). For example: ```yaml trainingInput: scheduling: maxRunningTime: 7200s ```
         */
        maxRunningTime?: string | null;
        /**
         * Optional. The maximum job wait time, expressed in seconds. The field can contain up to nine fractional digits, terminated by `s`. If not specified, there is no limit to the wait time. The minimum for this field is `1800s` (30 minutes). If the training job has not entered the `RUNNING` state after this duration, AI Platform Training cancels it. After the job begins running, it can no longer be cancelled due to the maximum wait time. Therefore the duration limited by this field does not overlap with the duration limited by Scheduling.max_running_time. For example, if the job temporarily stops running and retries due to a [VM restart](/ai-platform/training/docs/overview#restarts), this cannot lead to a maximum wait time cancellation. However, independently of this constraint, AI Platform Training might stop a job if there are too many retries due to exhausted resources in a region. The following example describes how you might use this field: To cancel your job if it doesn't start running within 1 hour, set this field to `3600s` (1 hour * 60 minutes / hour * 60 seconds / minute). If the job is still in the `QUEUED` or `PREPARING` state after an hour of waiting, AI Platform Training cancels the job. If you submit your training job using the `gcloud` tool, you can [specify this field in a `config.yaml` file](/ai-platform/training/docs/training-jobs#formatting_your_configuration_parameters). For example: ```yaml trainingInput: scheduling: maxWaitTime: 3600s ```
         */
        maxWaitTime?: string | null;
        /**
         * Optional. Job scheduling will be based on this priority, which in the range [0, 1000]. The bigger the number, the higher the priority. Default to 0 if not set. If there are multiple jobs requesting same type of accelerators, the high priority job will be scheduled prior to ones with low priority.
         */
        priority?: number | null;
    }
    /**
     * Request message for the SetDefaultVersion request.
     */
    export interface Schema$GoogleCloudMlV1__SetDefaultVersionRequest {
    }
    export interface Schema$GoogleCloudMlV1__StopTrialRequest {
    }
    /**
     * A message representing a Study.
     */
    export interface Schema$GoogleCloudMlV1__Study {
        /**
         * Output only. Time at which the study was created.
         */
        createTime?: string | null;
        /**
         * Output only. A human readable reason why the Study is inactive. This should be empty if a study is ACTIVE or COMPLETED.
         */
        inactiveReason?: string | null;
        /**
         * Output only. The name of a study.
         */
        name?: string | null;
        /**
         * Output only. The detailed state of a study.
         */
        state?: string | null;
        /**
         * Required. Configuration of the study.
         */
        studyConfig?: Schema$GoogleCloudMlV1__StudyConfig;
    }
    /**
     * Represents configuration of a study.
     */
    export interface Schema$GoogleCloudMlV1__StudyConfig {
        /**
         * The search algorithm specified for the study.
         */
        algorithm?: string | null;
        /**
         * Configuration for automated stopping of unpromising Trials.
         */
        automatedStoppingConfig?: Schema$GoogleCloudMlV1__AutomatedStoppingConfig;
        /**
         * Metric specs for the study.
         */
        metrics?: Schema$GoogleCloudMlV1_StudyConfig_MetricSpec[];
        /**
         * Required. The set of parameters to tune.
         */
        parameters?: Schema$GoogleCloudMlV1_StudyConfig_ParameterSpec[];
    }
    /**
     * Metadata field of a google.longrunning.Operation associated with a SuggestTrialsRequest.
     */
    export interface Schema$GoogleCloudMlV1__SuggestTrialsMetadata {
        /**
         * The identifier of the client that is requesting the suggestion.
         */
        clientId?: string | null;
        /**
         * The time operation was submitted.
         */
        createTime?: string | null;
        /**
         * The name of the study that the trial belongs to.
         */
        study?: string | null;
        /**
         * The number of suggestions requested.
         */
        suggestionCount?: number | null;
    }
    /**
     * The request message for the SuggestTrial service method.
     */
    export interface Schema$GoogleCloudMlV1__SuggestTrialsRequest {
        /**
         * Required. The identifier of the client that is requesting the suggestion. If multiple SuggestTrialsRequests have the same `client_id`, the service will return the identical suggested trial if the trial is pending, and provide a new trial if the last suggested trial was completed.
         */
        clientId?: string | null;
        /**
         * Required. The number of suggestions requested.
         */
        suggestionCount?: number | null;
    }
    /**
     * This message will be placed in the response field of a completed google.longrunning.Operation associated with a SuggestTrials request.
     */
    export interface Schema$GoogleCloudMlV1__SuggestTrialsResponse {
        /**
         * The time at which operation processing completed.
         */
        endTime?: string | null;
        /**
         * The time at which the operation was started.
         */
        startTime?: string | null;
        /**
         * The state of the study.
         */
        studyState?: string | null;
        /**
         * A list of trials.
         */
        trials?: Schema$GoogleCloudMlV1__Trial[];
    }
    /**
     * Represents input parameters for a training job. When using the gcloud command to submit your training job, you can specify the input parameters as command-line arguments and/or in a YAML configuration file referenced from the --config command-line argument. For details, see the guide to [submitting a training job](/ai-platform/training/docs/training-jobs).
     */
    export interface Schema$GoogleCloudMlV1__TrainingInput {
        /**
         * Optional. Command-line arguments passed to the training application when it starts. If your job uses a custom container, then the arguments are passed to the container's `ENTRYPOINT` command.
         */
        args?: string[] | null;
        /**
         * Optional. Whether you want AI Platform Training to enable [interactive shell access](https://cloud.google.com/ai-platform/training/docs/monitor-debug-interactive-shell) to training containers. If set to `true`, you can access interactive shells at the URIs given by TrainingOutput.web_access_uris or HyperparameterOutput.web_access_uris (within TrainingOutput.trials).
         */
        enableWebAccess?: boolean | null;
        /**
         * Optional. Options for using customer-managed encryption keys (CMEK) to protect resources created by a training job, instead of using Google's default encryption. If this is set, then all resources created by the training job will be encrypted with the customer-managed encryption key that you specify. [Learn how and when to use CMEK with AI Platform Training](/ai-platform/training/docs/cmek).
         */
        encryptionConfig?: Schema$GoogleCloudMlV1__EncryptionConfig;
        /**
         * Optional. The configuration for evaluators. You should only set `evaluatorConfig.acceleratorConfig` if `evaluatorType` is set to a Compute Engine machine type. [Learn about restrictions on accelerator configurations for training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu) Set `evaluatorConfig.imageUri` only if you build a custom image for your evaluator. If `evaluatorConfig.imageUri` has not been set, AI Platform uses the value of `masterConfig.imageUri`. Learn more about [configuring custom containers](/ai-platform/training/docs/distributed-training-containers).
         */
        evaluatorConfig?: Schema$GoogleCloudMlV1__ReplicaConfig;
        /**
         * Optional. The number of evaluator replicas to use for the training job. Each replica in the cluster will be of the type specified in `evaluator_type`. This value can only be used when `scale_tier` is set to `CUSTOM`. If you set this value, you must also set `evaluator_type`. The default value is zero.
         */
        evaluatorCount?: string | null;
        /**
         * Optional. Specifies the type of virtual machine to use for your training job's evaluator nodes. The supported values are the same as those described in the entry for `masterType`. This value must be consistent with the category of machine type that `masterType` uses. In other words, both must be Compute Engine machine types or both must be legacy machine types. This value must be present when `scaleTier` is set to `CUSTOM` and `evaluatorCount` is greater than zero.
         */
        evaluatorType?: string | null;
        /**
         * Optional. The set of Hyperparameters to tune.
         */
        hyperparameters?: Schema$GoogleCloudMlV1__HyperparameterSpec;
        /**
         * Optional. A Google Cloud Storage path in which to store training outputs and other data needed for training. This path is passed to your TensorFlow program as the '--job-dir' command-line argument. The benefit of specifying this field is that Cloud ML validates the path for use in training.
         */
        jobDir?: string | null;
        /**
         * Optional. The configuration for your master worker. You should only set `masterConfig.acceleratorConfig` if `masterType` is set to a Compute Engine machine type. Learn about [restrictions on accelerator configurations for training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu) Set `masterConfig.imageUri` only if you build a custom image. Only one of `masterConfig.imageUri` and `runtimeVersion` should be set. Learn more about [configuring custom containers](/ai-platform/training/docs/distributed-training-containers).
         */
        masterConfig?: Schema$GoogleCloudMlV1__ReplicaConfig;
        /**
         * Optional. Specifies the type of virtual machine to use for your training job's master worker. You must specify this field when `scaleTier` is set to `CUSTOM`. You can use certain Compute Engine machine types directly in this field. See the [list of compatible Compute Engine machine types](/ai-platform/training/docs/machine-types#compute-engine-machine-types). Alternatively, you can use the certain legacy machine types in this field. See the [list of legacy machine types](/ai-platform/training/docs/machine-types#legacy-machine-types). Finally, if you want to use a TPU for training, specify `cloud_tpu` in this field. Learn more about the [special configuration options for training with TPUs](/ai-platform/training/docs/using-tpus#configuring_a_custom_tpu_machine).
         */
        masterType?: string | null;
        /**
         * Optional. The full name of the [Compute Engine network](/vpc/docs/vpc) to which the Job is peered. For example, `projects/12345/global/networks/myVPC`. The format of this field is `projects/{project\}/global/networks/{network\}`, where {project\} is a project number (like `12345`) and {network\} is network name. Private services access must already be configured for the network. If left unspecified, the Job is not peered with any network. [Learn about using VPC Network Peering.](/ai-platform/training/docs/vpc-peering).
         */
        network?: string | null;
        /**
         * Required. The Google Cloud Storage location of the packages with the training program and any additional dependencies. The maximum number of package URIs is 100.
         */
        packageUris?: string[] | null;
        /**
         * Optional. The configuration for parameter servers. You should only set `parameterServerConfig.acceleratorConfig` if `parameterServerType` is set to a Compute Engine machine type. [Learn about restrictions on accelerator configurations for training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu) Set `parameterServerConfig.imageUri` only if you build a custom image for your parameter server. If `parameterServerConfig.imageUri` has not been set, AI Platform uses the value of `masterConfig.imageUri`. Learn more about [configuring custom containers](/ai-platform/training/docs/distributed-training-containers).
         */
        parameterServerConfig?: Schema$GoogleCloudMlV1__ReplicaConfig;
        /**
         * Optional. The number of parameter server replicas to use for the training job. Each replica in the cluster will be of the type specified in `parameter_server_type`. This value can only be used when `scale_tier` is set to `CUSTOM`. If you set this value, you must also set `parameter_server_type`. The default value is zero.
         */
        parameterServerCount?: string | null;
        /**
         * Optional. Specifies the type of virtual machine to use for your training job's parameter server. The supported values are the same as those described in the entry for `master_type`. This value must be consistent with the category of machine type that `masterType` uses. In other words, both must be Compute Engine machine types or both must be legacy machine types. This value must be present when `scaleTier` is set to `CUSTOM` and `parameter_server_count` is greater than zero.
         */
        parameterServerType?: string | null;
        /**
         * Required. The Python module name to run after installing the packages.
         */
        pythonModule?: string | null;
        /**
         * Optional. The version of Python used in training. You must either specify this field or specify `masterConfig.imageUri`. The following Python versions are available: * Python '3.7' is available when `runtime_version` is set to '1.15' or later. * Python '3.5' is available when `runtime_version` is set to a version from '1.4' to '1.14'. * Python '2.7' is available when `runtime_version` is set to '1.15' or earlier. Read more about the Python versions available for [each runtime version](/ml-engine/docs/runtime-version-list).
         */
        pythonVersion?: string | null;
        /**
         * Required. The region to run the training job in. See the [available regions](/ai-platform/training/docs/regions) for AI Platform Training.
         */
        region?: string | null;
        /**
         * Optional. The AI Platform runtime version to use for training. You must either specify this field or specify `masterConfig.imageUri`. For more information, see the [runtime version list](/ai-platform/training/docs/runtime-version-list) and learn [how to manage runtime versions](/ai-platform/training/docs/versioning).
         */
        runtimeVersion?: string | null;
        /**
         * Required. Specifies the machine types, the number of replicas for workers and parameter servers.
         */
        scaleTier?: string | null;
        /**
         * Optional. Scheduling options for a training job.
         */
        scheduling?: Schema$GoogleCloudMlV1__Scheduling;
        /**
         * Optional. The email address of a service account to use when running the training appplication. You must have the `iam.serviceAccounts.actAs` permission for the specified service account. In addition, the AI Platform Training Google-managed service account must have the `roles/iam.serviceAccountAdmin` role for the specified service account. [Learn more about configuring a service account.](/ai-platform/training/docs/custom-service-account) If not specified, the AI Platform Training Google-managed service account is used by default.
         */
        serviceAccount?: string | null;
        /**
         * Optional. Use `chief` instead of `master` in the `TF_CONFIG` environment variable when training with a custom container. Defaults to `false`. [Learn more about this field.](/ai-platform/training/docs/distributed-training-details#chief-versus-master) This field has no effect for training jobs that don't use a custom container.
         */
        useChiefInTfConfig?: boolean | null;
        /**
         * Optional. The configuration for workers. You should only set `workerConfig.acceleratorConfig` if `workerType` is set to a Compute Engine machine type. [Learn about restrictions on accelerator configurations for training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu) Set `workerConfig.imageUri` only if you build a custom image for your worker. If `workerConfig.imageUri` has not been set, AI Platform uses the value of `masterConfig.imageUri`. Learn more about [configuring custom containers](/ai-platform/training/docs/distributed-training-containers).
         */
        workerConfig?: Schema$GoogleCloudMlV1__ReplicaConfig;
        /**
         * Optional. The number of worker replicas to use for the training job. Each replica in the cluster will be of the type specified in `worker_type`. This value can only be used when `scale_tier` is set to `CUSTOM`. If you set this value, you must also set `worker_type`. The default value is zero.
         */
        workerCount?: string | null;
        /**
         * Optional. Specifies the type of virtual machine to use for your training job's worker nodes. The supported values are the same as those described in the entry for `masterType`. This value must be consistent with the category of machine type that `masterType` uses. In other words, both must be Compute Engine machine types or both must be legacy machine types. If you use `cloud_tpu` for this value, see special instructions for [configuring a custom TPU machine](/ml-engine/docs/tensorflow/using-tpus#configuring_a_custom_tpu_machine). This value must be present when `scaleTier` is set to `CUSTOM` and `workerCount` is greater than zero.
         */
        workerType?: string | null;
    }
    /**
     * Represents results of a training job. Output only.
     */
    export interface Schema$GoogleCloudMlV1__TrainingOutput {
        /**
         * Details related to built-in algorithms jobs. Only set for built-in algorithms jobs.
         */
        builtInAlgorithmOutput?: Schema$GoogleCloudMlV1__BuiltInAlgorithmOutput;
        /**
         * The number of hyperparameter tuning trials that completed successfully. Only set for hyperparameter tuning jobs.
         */
        completedTrialCount?: string | null;
        /**
         * The amount of ML units consumed by the job.
         */
        consumedMLUnits?: number | null;
        /**
         * The TensorFlow summary tag name used for optimizing hyperparameter tuning trials. See [`HyperparameterSpec.hyperparameterMetricTag`](#HyperparameterSpec.FIELDS.hyperparameter_metric_tag) for more information. Only set for hyperparameter tuning jobs.
         */
        hyperparameterMetricTag?: string | null;
        /**
         * Whether this job is a built-in Algorithm job.
         */
        isBuiltInAlgorithmJob?: boolean | null;
        /**
         * Whether this job is a hyperparameter tuning job.
         */
        isHyperparameterTuningJob?: boolean | null;
        /**
         * Results for individual Hyperparameter trials. Only set for hyperparameter tuning jobs.
         */
        trials?: Schema$GoogleCloudMlV1__HyperparameterOutput[];
        /**
         * Output only. URIs for accessing [interactive shells](https://cloud.google.com/ai-platform/training/docs/monitor-debug-interactive-shell) (one URI for each training node). Only available if training_input.enable_web_access is `true`. The keys are names of each node in the training job; for example, `master-replica-0` for the master node, `worker-replica-0` for the first worker, and `ps-replica-0` for the first parameter server. The values are the URIs for each node's interactive shell.
         */
        webAccessUris?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A message representing a trial.
     */
    export interface Schema$GoogleCloudMlV1__Trial {
        /**
         * Output only. The identifier of the client that originally requested this trial.
         */
        clientId?: string | null;
        /**
         * Output only. Time at which the trial's status changed to COMPLETED.
         */
        endTime?: string | null;
        /**
         * The final measurement containing the objective value.
         */
        finalMeasurement?: Schema$GoogleCloudMlV1__Measurement;
        /**
         * Output only. A human readable string describing why the trial is infeasible. This should only be set if trial_infeasible is true.
         */
        infeasibleReason?: string | null;
        /**
         * A list of measurements that are strictly lexicographically ordered by their induced tuples (steps, elapsed_time). These are used for early stopping computations.
         */
        measurements?: Schema$GoogleCloudMlV1__Measurement[];
        /**
         * Output only. Name of the trial assigned by the service.
         */
        name?: string | null;
        /**
         * The parameters of the trial.
         */
        parameters?: Schema$GoogleCloudMlV1_Trial_Parameter[];
        /**
         * Output only. Time at which the trial was started.
         */
        startTime?: string | null;
        /**
         * The detailed state of a trial.
         */
        state?: string | null;
        /**
         * Output only. If true, the parameters in this trial are not attempted again.
         */
        trialInfeasible?: boolean | null;
    }
    /**
     * Represents a version of the model. Each version is a trained model deployed in the cloud, ready to handle prediction requests. A model can have multiple versions. You can get information about all of the versions of a given model by calling projects.models.versions.list.
     */
    export interface Schema$GoogleCloudMlV1__Version {
        /**
         * Optional. Accelerator config for using GPUs for online prediction (beta). Only specify this field if you have specified a Compute Engine (N1) machine type in the `machineType` field. Learn more about [using GPUs for online prediction](/ml-engine/docs/machine-types-online-prediction#gpus).
         */
        acceleratorConfig?: Schema$GoogleCloudMlV1__AcceleratorConfig;
        /**
         * Automatically scale the number of nodes used to serve the model in response to increases and decreases in traffic. Care should be taken to ramp up traffic according to the model's ability to scale or you will start seeing increases in latency and 429 response codes.
         */
        autoScaling?: Schema$GoogleCloudMlV1__AutoScaling;
        /**
         * Optional. Specifies a custom container to use for serving predictions. If you specify this field, then `machineType` is required. If you specify this field, then `deploymentUri` is optional. If you specify this field, then you must not specify `runtimeVersion`, `packageUris`, `framework`, `pythonVersion`, or `predictionClass`.
         */
        container?: Schema$GoogleCloudMlV1__ContainerSpec;
        /**
         * Output only. The time the version was created.
         */
        createTime?: string | null;
        /**
         * The Cloud Storage URI of a directory containing trained model artifacts to be used to create the model version. See the [guide to deploying models](/ai-platform/prediction/docs/deploying-models) for more information. The total number of files under this directory must not exceed 1000. During projects.models.versions.create, AI Platform Prediction copies all files from the specified directory to a location managed by the service. From then on, AI Platform Prediction uses these copies of the model artifacts to serve predictions, not the original files in Cloud Storage, so this location is useful only as a historical record. If you specify container, then this field is optional. Otherwise, it is required. Learn [how to use this field with a custom container](/ai-platform/prediction/docs/custom-container-requirements#artifacts).
         */
        deploymentUri?: string | null;
        /**
         * Optional. The description specified for the version when it was created.
         */
        description?: string | null;
        /**
         * Output only. The details of a failure or a cancellation.
         */
        errorMessage?: string | null;
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a model from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform model updates in order to avoid race conditions: An `etag` is returned in the response to `GetVersion`, and systems are expected to put that etag in the request to `UpdateVersion` to ensure that their change will be applied to the model as intended.
         */
        etag?: string | null;
        /**
         * Optional. Configures explainability features on the model's version. Some explanation features require additional metadata to be loaded as part of the model payload.
         */
        explanationConfig?: Schema$GoogleCloudMlV1__ExplanationConfig;
        /**
         * Optional. The machine learning framework AI Platform uses to train this version of the model. Valid values are `TENSORFLOW`, `SCIKIT_LEARN`, `XGBOOST`. If you do not specify a framework, AI Platform will analyze files in the deployment_uri to determine a framework. If you choose `SCIKIT_LEARN` or `XGBOOST`, you must also set the runtime version of the model to 1.4 or greater. Do **not** specify a framework if you're deploying a [custom prediction routine](/ai-platform/prediction/docs/custom-prediction-routines) or if you're using a [custom container](/ai-platform/prediction/docs/use-custom-container).
         */
        framework?: string | null;
        /**
         * Output only. If true, this version will be used to handle prediction requests that do not specify a version. You can change the default version by calling projects.methods.versions.setDefault.
         */
        isDefault?: boolean | null;
        /**
         * Optional. One or more labels that you can add, to organize your model versions. Each label is a key-value pair, where both the key and the value are arbitrary strings that you supply. For more information, see the documentation on using labels. Note that this field is not updatable for mls1* models.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The [AI Platform (Unified) `Model`](https://cloud.google.com/ai-platform-unified/docs/reference/rest/v1beta1/projects.locations.models) ID for the last [model migration](https://cloud.google.com/ai-platform-unified/docs/start/migrating-to-ai-platform-unified).
         */
        lastMigrationModelId?: string | null;
        /**
         * Output only. The last time this version was successfully [migrated to AI Platform (Unified)](https://cloud.google.com/ai-platform-unified/docs/start/migrating-to-ai-platform-unified).
         */
        lastMigrationTime?: string | null;
        /**
         * Output only. The time the version was last used for prediction.
         */
        lastUseTime?: string | null;
        /**
         * Optional. The type of machine on which to serve the model. Currently only applies to online prediction service. To learn about valid values for this field, read [Choosing a machine type for online prediction](/ai-platform/prediction/docs/machine-types-online-prediction). If this field is not specified and you are using a [regional endpoint](/ai-platform/prediction/docs/regional-endpoints), then the machine type defaults to `n1-standard-2`. If this field is not specified and you are using the global endpoint (`ml.googleapis.com`), then the machine type defaults to `mls1-c1-m2`.
         */
        machineType?: string | null;
        /**
         * Manually select the number of nodes to use for serving the model. You should generally use `auto_scaling` with an appropriate `min_nodes` instead, but this option is available if you want more predictable billing. Beware that latency and error rates will increase if the traffic exceeds that capability of the system to serve it based on the selected number of nodes.
         */
        manualScaling?: Schema$GoogleCloudMlV1__ManualScaling;
        /**
         * Required. The name specified for the version when it was created. The version name must be unique within the model it is created in.
         */
        name?: string | null;
        /**
         * Optional. Cloud Storage paths (`gs://…`) of packages for [custom prediction routines](/ml-engine/docs/tensorflow/custom-prediction-routines) or [scikit-learn pipelines with custom code](/ml-engine/docs/scikit/exporting-for-prediction#custom-pipeline-code). For a custom prediction routine, one of these packages must contain your Predictor class (see [`predictionClass`](#Version.FIELDS.prediction_class)). Additionally, include any dependencies used by your Predictor or scikit-learn pipeline uses that are not already included in your selected [runtime version](/ml-engine/docs/tensorflow/runtime-version-list). If you specify this field, you must also set [`runtimeVersion`](#Version.FIELDS.runtime_version) to 1.4 or greater.
         */
        packageUris?: string[] | null;
        /**
         * Optional. The fully qualified name (module_name.class_name) of a class that implements the Predictor interface described in this reference field. The module containing this class should be included in a package provided to the [`packageUris` field](#Version.FIELDS.package_uris). Specify this field if and only if you are deploying a [custom prediction routine (beta)](/ml-engine/docs/tensorflow/custom-prediction-routines). If you specify this field, you must set [`runtimeVersion`](#Version.FIELDS.runtime_version) to 1.4 or greater and you must set `machineType` to a [legacy (MLS1) machine type](/ml-engine/docs/machine-types-online-prediction). The following code sample provides the Predictor interface: class Predictor(object): """Interface for constructing custom predictors.""" def predict(self, instances, **kwargs): """Performs custom prediction. Instances are the decoded values from the request. They have already been deserialized from JSON. Args: instances: A list of prediction input instances. **kwargs: A dictionary of keyword args provided as additional fields on the predict request body. Returns: A list of outputs containing the prediction results. This list must be JSON serializable. """ raise NotImplementedError() @classmethod def from_path(cls, model_dir): """Creates an instance of Predictor using the given path. Loading of the predictor should be done in this method. Args: model_dir: The local directory that contains the exported model file along with any additional files uploaded when creating the version resource. Returns: An instance implementing this Predictor class. """ raise NotImplementedError() Learn more about [the Predictor interface and custom prediction routines](/ml-engine/docs/tensorflow/custom-prediction-routines).
         */
        predictionClass?: string | null;
        /**
         * Required. The version of Python used in prediction. The following Python versions are available: * Python '3.7' is available when `runtime_version` is set to '1.15' or later. * Python '3.5' is available when `runtime_version` is set to a version from '1.4' to '1.14'. * Python '2.7' is available when `runtime_version` is set to '1.15' or earlier. Read more about the Python versions available for [each runtime version](/ml-engine/docs/runtime-version-list).
         */
        pythonVersion?: string | null;
        /**
         * Optional. *Only* specify this field in a projects.models.versions.patch request. Specifying it in a projects.models.versions.create request has no effect. Configures the request-response pair logging on predictions from this Version.
         */
        requestLoggingConfig?: Schema$GoogleCloudMlV1__RequestLoggingConfig;
        /**
         * Optional. Specifies paths on a custom container's HTTP server where AI Platform Prediction sends certain requests. If you specify this field, then you must also specify the `container` field. If you specify the `container` field and do not specify this field, it defaults to the following: ```json { "predict": "/v1/models/MODEL/versions/VERSION:predict", "health": "/v1/models/MODEL/versions/VERSION" \} ``` See RouteMap for more details about these default values.
         */
        routes?: Schema$GoogleCloudMlV1__RouteMap;
        /**
         * Required. The AI Platform runtime version to use for this deployment. For more information, see the [runtime version list](/ml-engine/docs/runtime-version-list) and [how to manage runtime versions](/ml-engine/docs/versioning).
         */
        runtimeVersion?: string | null;
        /**
         * Optional. Specifies the service account for resource access control. If you specify this field, then you must also specify either the `containerSpec` or the `predictionClass` field. Learn more about [using a custom service account](/ai-platform/prediction/docs/custom-service-account).
         */
        serviceAccount?: string | null;
        /**
         * Output only. The state of a version.
         */
        state?: string | null;
    }
    /**
     * Attributes credit by computing the XRAI taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1906.02825 Currently only implemented for models with natural image inputs.
     */
    export interface Schema$GoogleCloudMlV1__XraiAttribution {
        /**
         * Number of steps for approximating the path integral. A good value to start is 50 and gradually increase until the sum to diff property is met within the desired error range.
         */
        numIntegralSteps?: number | null;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$GoogleIamV1__AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$GoogleIamV1__AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$GoogleIamV1__AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$GoogleIamV1__Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$GoogleType__Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$GoogleIamV1__Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$GoogleIamV1__AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$GoogleIamV1__Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$GoogleIamV1__SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$GoogleIamV1__Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1__TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1__TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$GoogleLongrunning__ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$GoogleLongrunning__Operation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunning__Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$GoogleRpc__Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$GoogleProtobuf__Empty {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$GoogleRpc__Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$GoogleType__Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        jobs: Resource$Projects$Jobs;
        locations: Resource$Projects$Locations;
        models: Resource$Projects$Models;
        operations: Resource$Projects$Operations;
        constructor(context: APIRequestContext);
        /**
         * Performs explanation on the data in the request. {% dynamic include "/ai-platform/includes/___explain-request" %\}
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        explain(params: Params$Resource$Projects$Explain, options: StreamMethodOptions): GaxiosPromise<Readable>;
        explain(params?: Params$Resource$Projects$Explain, options?: MethodOptions): GaxiosPromise<Schema$GoogleApi__HttpBody>;
        explain(params: Params$Resource$Projects$Explain, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        explain(params: Params$Resource$Projects$Explain, options: MethodOptions | BodyResponseCallback<Schema$GoogleApi__HttpBody>, callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
        explain(params: Params$Resource$Projects$Explain, callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
        explain(callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
        /**
         * Get the service account information associated with your project. You need this information in order to grant the service account permissions for the Google Cloud Storage location where you put your model training code for training the model with Google Cloud Machine Learning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getConfig(params: Params$Resource$Projects$Getconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getConfig(params?: Params$Resource$Projects$Getconfig, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__GetConfigResponse>;
        getConfig(params: Params$Resource$Projects$Getconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getConfig(params: Params$Resource$Projects$Getconfig, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__GetConfigResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__GetConfigResponse>): void;
        getConfig(params: Params$Resource$Projects$Getconfig, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__GetConfigResponse>): void;
        getConfig(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__GetConfigResponse>): void;
        /**
         * Performs online prediction on the data in the request. {% dynamic include "/ai-platform/includes/___predict-request" %\}
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        predict(params: Params$Resource$Projects$Predict, options: StreamMethodOptions): GaxiosPromise<Readable>;
        predict(params?: Params$Resource$Projects$Predict, options?: MethodOptions): GaxiosPromise<Schema$GoogleApi__HttpBody>;
        predict(params: Params$Resource$Projects$Predict, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        predict(params: Params$Resource$Projects$Predict, options: MethodOptions | BodyResponseCallback<Schema$GoogleApi__HttpBody>, callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
        predict(params: Params$Resource$Projects$Predict, callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
        predict(callback: BodyResponseCallback<Schema$GoogleApi__HttpBody>): void;
    }
    export interface Params$Resource$Projects$Explain extends StandardParameters {
        /**
         * Required. The resource name of a model or a version. Authorization: requires the `predict` permission on the specified resource.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__ExplainRequest;
    }
    export interface Params$Resource$Projects$Getconfig extends StandardParameters {
        /**
         * Required. The project name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Predict extends StandardParameters {
        /**
         * Required. The resource name of a model or a version. Authorization: requires the `predict` permission on the specified resource.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__PredictRequest;
    }
    export class Resource$Projects$Jobs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Cancels a running job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Jobs$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Jobs$Cancel, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobuf__Empty>;
        cancel(params: Params$Resource$Projects$Jobs$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Jobs$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobuf__Empty>, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(params: Params$Resource$Projects$Jobs$Cancel, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        /**
         * Creates a training or a batch prediction job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Jobs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Jobs$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Job>;
        create(params: Params$Resource$Projects$Jobs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Jobs$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Job>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        create(params: Params$Resource$Projects$Jobs$Create, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        create(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        /**
         * Describes a job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Jobs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Jobs$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Job>;
        get(params: Params$Resource$Projects$Jobs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Jobs$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Job>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        get(params: Params$Resource$Projects$Jobs$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Jobs$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Jobs$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__Policy>;
        getIamPolicy(params: Params$Resource$Projects$Jobs$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Jobs$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Jobs$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        /**
         * Lists the jobs in the project. If there are no jobs that match the request parameters, the list request returns an empty response body: {\}.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Jobs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Jobs$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListJobsResponse>;
        list(params: Params$Resource$Projects$Jobs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Jobs$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListJobsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListJobsResponse>): void;
        list(params: Params$Resource$Projects$Jobs$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListJobsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListJobsResponse>): void;
        /**
         * Updates a specific job resource. Currently the only supported fields to update are `labels`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Jobs$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Jobs$Patch, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Job>;
        patch(params: Params$Resource$Projects$Jobs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Jobs$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Job>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        patch(params: Params$Resource$Projects$Jobs$Patch, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Job>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Jobs$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Jobs$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__Policy>;
        setIamPolicy(params: Params$Resource$Projects$Jobs$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Jobs$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Jobs$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Jobs$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Jobs$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Jobs$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Jobs$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Jobs$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Jobs$Cancel extends StandardParameters {
        /**
         * Required. The name of the job to cancel.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__CancelJobRequest;
    }
    export interface Params$Resource$Projects$Jobs$Create extends StandardParameters {
        /**
         * Required. The project name.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Job;
    }
    export interface Params$Resource$Projects$Jobs$Get extends StandardParameters {
        /**
         * Required. The name of the job to get the description of.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Jobs$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Jobs$List extends StandardParameters {
        /**
         * Optional. Specifies the subset of jobs to retrieve. You can filter on the value of one or more attributes of the job object. For example, retrieve jobs with a job identifier that starts with 'census': gcloud ai-platform jobs list --filter='jobId:census*' List all failed jobs with names that start with 'rnn': gcloud ai-platform jobs list --filter='jobId:rnn* AND state:FAILED' For more examples, see the guide to monitoring jobs.
         */
        filter?: string;
        /**
         * Optional. The number of jobs to retrieve per "page" of results. If there are more remaining results than this number, the response message will contain a valid value in the `next_page_token` field. The default value is 20, and the maximum page size is 100.
         */
        pageSize?: number;
        /**
         * Optional. A page token to request the next page of results. You get the token from the `next_page_token` field of the response from the previous call.
         */
        pageToken?: string;
        /**
         * Required. The name of the project for which to list jobs.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Jobs$Patch extends StandardParameters {
        /**
         * Required. The job name.
         */
        name?: string;
        /**
         * Required. Specifies the path, relative to `Job`, of the field to update. To adopt etag mechanism, include `etag` field in the mask, and include the `etag` value in your job resource. For example, to change the labels of a job, the `update_mask` parameter would be specified as `labels`, `etag`, and the `PATCH` request body would specify the new value, as follows: { "labels": { "owner": "Google", "color": "Blue" \} "etag": "33a64df551425fcc55e4d42a148795d9f25f89d4" \} If `etag` matches the one on the server, the labels of the job will be replaced with the given ones, and the server end `etag` will be recalculated. Currently the only supported update masks are `labels` and `etag`.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Job;
    }
    export interface Params$Resource$Projects$Jobs$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1__SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Jobs$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1__TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        operations: Resource$Projects$Locations$Operations;
        studies: Resource$Projects$Locations$Studies;
        constructor(context: APIRequestContext);
        /**
         * Get the complete list of CMLE capabilities in a location, along with their location-specific properties.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Location>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Location>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Location>): void;
        /**
         * List all locations that provides at least one type of CMLE capability.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListLocationsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Required. The name of the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. The number of locations to retrieve per "page" of results. If there are more remaining results than this number, the response message will contain a valid value in the `next_page_token` field. The default value is 20, and the maximum page size is 100.
         */
        pageSize?: number;
        /**
         * Optional. A page token to request the next page of results. You get the token from the `next_page_token` field of the response from the previous call.
         */
        pageToken?: string;
        /**
         * Required. The name of the project for which available locations are to be listed (since some locations might be whitelisted for specific projects).
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobuf__Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobuf__Empty>, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Projects$Locations$Studies {
        context: APIRequestContext;
        trials: Resource$Projects$Locations$Studies$Trials;
        constructor(context: APIRequestContext);
        /**
         * Creates a study.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Studies$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Studies$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Study>;
        create(params: Params$Resource$Projects$Locations$Studies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Studies$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Study>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        create(params: Params$Resource$Projects$Locations$Studies$Create, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        create(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        /**
         * Deletes a study.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Studies$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Studies$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobuf__Empty>;
        delete(params: Params$Resource$Projects$Locations$Studies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Studies$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobuf__Empty>, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Studies$Delete, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        /**
         * Gets a study.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Studies$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Studies$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Study>;
        get(params: Params$Resource$Projects$Locations$Studies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Studies$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Study>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        get(params: Params$Resource$Projects$Locations$Studies$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Study>): void;
        /**
         * Lists all the studies in a region for an associated project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Studies$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Studies$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListStudiesResponse>;
        list(params: Params$Resource$Projects$Locations$Studies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Studies$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListStudiesResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListStudiesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Studies$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListStudiesResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListStudiesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Studies$Create extends StandardParameters {
        /**
         * Required. The project and location that the study belongs to. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Required. The ID to use for the study, which will become the final component of the study's resource name.
         */
        studyId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Study;
    }
    export interface Params$Resource$Projects$Locations$Studies$Delete extends StandardParameters {
        /**
         * Required. The study name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Studies$Get extends StandardParameters {
        /**
         * Required. The study name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Studies$List extends StandardParameters {
        /**
         * Required. The project and location that the study belongs to. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Studies$Trials {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Adds a measurement of the objective metrics to a trial. This measurement is assumed to have been taken before the trial is complete.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        addMeasurement(params: Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement, options: StreamMethodOptions): GaxiosPromise<Readable>;
        addMeasurement(params?: Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Trial>;
        addMeasurement(params: Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        addMeasurement(params: Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        addMeasurement(params: Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        addMeasurement(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        /**
         * Checks whether a trial should stop or not. Returns a long-running operation. When the operation is successful, it will contain a CheckTrialEarlyStoppingStateResponse.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        checkEarlyStoppingState(params: Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        checkEarlyStoppingState(params?: Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        checkEarlyStoppingState(params: Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        checkEarlyStoppingState(params: Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        checkEarlyStoppingState(params: Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        checkEarlyStoppingState(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Marks a trial as complete.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        complete(params: Params$Resource$Projects$Locations$Studies$Trials$Complete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        complete(params?: Params$Resource$Projects$Locations$Studies$Trials$Complete, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Trial>;
        complete(params: Params$Resource$Projects$Locations$Studies$Trials$Complete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        complete(params: Params$Resource$Projects$Locations$Studies$Trials$Complete, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        complete(params: Params$Resource$Projects$Locations$Studies$Trials$Complete, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        complete(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        /**
         * Adds a user provided trial to a study.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Studies$Trials$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Studies$Trials$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Trial>;
        create(params: Params$Resource$Projects$Locations$Studies$Trials$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Studies$Trials$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        create(params: Params$Resource$Projects$Locations$Studies$Trials$Create, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        create(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        /**
         * Deletes a trial.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Studies$Trials$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Studies$Trials$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobuf__Empty>;
        delete(params: Params$Resource$Projects$Locations$Studies$Trials$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Studies$Trials$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobuf__Empty>, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Studies$Trials$Delete, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        /**
         * Gets a trial.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Studies$Trials$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Studies$Trials$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Trial>;
        get(params: Params$Resource$Projects$Locations$Studies$Trials$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Studies$Trials$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        get(params: Params$Resource$Projects$Locations$Studies$Trials$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        /**
         * Lists the trials associated with a study.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Studies$Trials$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Studies$Trials$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListTrialsResponse>;
        list(params: Params$Resource$Projects$Locations$Studies$Trials$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Studies$Trials$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListTrialsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListTrialsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Studies$Trials$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListTrialsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListTrialsResponse>): void;
        /**
         * Lists the pareto-optimal trials for multi-objective study or the optimal trials for single-objective study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listOptimalTrials(params: Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials, options: StreamMethodOptions): GaxiosPromise<Readable>;
        listOptimalTrials(params?: Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListOptimalTrialsResponse>;
        listOptimalTrials(params: Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listOptimalTrials(params: Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListOptimalTrialsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListOptimalTrialsResponse>): void;
        listOptimalTrials(params: Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListOptimalTrialsResponse>): void;
        listOptimalTrials(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListOptimalTrialsResponse>): void;
        /**
         * Stops a trial.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Projects$Locations$Studies$Trials$Stop, options: StreamMethodOptions): GaxiosPromise<Readable>;
        stop(params?: Params$Resource$Projects$Locations$Studies$Trials$Stop, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Trial>;
        stop(params: Params$Resource$Projects$Locations$Studies$Trials$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Projects$Locations$Studies$Trials$Stop, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        stop(params: Params$Resource$Projects$Locations$Studies$Trials$Stop, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        stop(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Trial>): void;
        /**
         * Adds one or more trials to a study, with parameter values suggested by AI Platform Vizier. Returns a long-running operation associated with the generation of trial suggestions. When this long-running operation succeeds, it will contain a SuggestTrialsResponse.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        suggest(params: Params$Resource$Projects$Locations$Studies$Trials$Suggest, options: StreamMethodOptions): GaxiosPromise<Readable>;
        suggest(params?: Params$Resource$Projects$Locations$Studies$Trials$Suggest, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        suggest(params: Params$Resource$Projects$Locations$Studies$Trials$Suggest, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        suggest(params: Params$Resource$Projects$Locations$Studies$Trials$Suggest, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        suggest(params: Params$Resource$Projects$Locations$Studies$Trials$Suggest, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        suggest(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Addmeasurement extends StandardParameters {
        /**
         * Required. The trial name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__AddTrialMeasurementRequest;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Checkearlystoppingstate extends StandardParameters {
        /**
         * Required. The trial name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__CheckTrialEarlyStoppingStateRequest;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Complete extends StandardParameters {
        /**
         * Required. The trial name.metat
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__CompleteTrialRequest;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Create extends StandardParameters {
        /**
         * Required. The name of the study that the trial belongs to.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Trial;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Delete extends StandardParameters {
        /**
         * Required. The trial name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Get extends StandardParameters {
        /**
         * Required. The trial name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$List extends StandardParameters {
        /**
         * Required. The name of the study that the trial belongs to.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Listoptimaltrials extends StandardParameters {
        /**
         * Required. The name of the study that the pareto-optimal trial belongs to.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__ListOptimalTrialsRequest;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Stop extends StandardParameters {
        /**
         * Required. The trial name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__StopTrialRequest;
    }
    export interface Params$Resource$Projects$Locations$Studies$Trials$Suggest extends StandardParameters {
        /**
         * Required. The name of the study that the trial belongs to.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__SuggestTrialsRequest;
    }
    export class Resource$Projects$Models {
        context: APIRequestContext;
        versions: Resource$Projects$Models$Versions;
        constructor(context: APIRequestContext);
        /**
         * Creates a model which will later contain one or more versions. You must add at least one version before you can request predictions from the model. Add versions by calling projects.models.versions.create.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Models$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Models$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Model>;
        create(params: Params$Resource$Projects$Models$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Models$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Model>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        create(params: Params$Resource$Projects$Models$Create, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        create(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        /**
         * Deletes a model. You can only delete a model if there are no versions in it. You can delete versions by calling projects.models.versions.delete.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Models$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Models$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        delete(params: Params$Resource$Projects$Models$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Models$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        delete(params: Params$Resource$Projects$Models$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Gets information about a model, including its name, the description (if set), and the default version (if at least one version of the model has been deployed).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Models$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Models$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Model>;
        get(params: Params$Resource$Projects$Models$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Models$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Model>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        get(params: Params$Resource$Projects$Models$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Model>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Models$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Models$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__Policy>;
        getIamPolicy(params: Params$Resource$Projects$Models$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Models$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Models$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        /**
         * Lists the models in a project. Each project can contain multiple models, and each model can have multiple versions. If there are no models that match the request parameters, the list request returns an empty response body: {\}.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Models$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Models$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListModelsResponse>;
        list(params: Params$Resource$Projects$Models$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Models$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListModelsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListModelsResponse>): void;
        list(params: Params$Resource$Projects$Models$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListModelsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListModelsResponse>): void;
        /**
         * Updates a specific model resource. Currently the only supported fields to update are `description` and `default_version.name`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Models$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Models$Patch, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        patch(params: Params$Resource$Projects$Models$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Models$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        patch(params: Params$Resource$Projects$Models$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Models$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Models$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__Policy>;
        setIamPolicy(params: Params$Resource$Projects$Models$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Models$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Models$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1__Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Models$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Models$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1__TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Models$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Models$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Models$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1__TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Models$Create extends StandardParameters {
        /**
         * Required. The project name.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Model;
    }
    export interface Params$Resource$Projects$Models$Delete extends StandardParameters {
        /**
         * Required. The name of the model.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Models$Get extends StandardParameters {
        /**
         * Required. The name of the model.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Models$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Models$List extends StandardParameters {
        /**
         * Optional. Specifies the subset of models to retrieve.
         */
        filter?: string;
        /**
         * Optional. The number of models to retrieve per "page" of results. If there are more remaining results than this number, the response message will contain a valid value in the `next_page_token` field. The default value is 20, and the maximum page size is 100.
         */
        pageSize?: number;
        /**
         * Optional. A page token to request the next page of results. You get the token from the `next_page_token` field of the response from the previous call.
         */
        pageToken?: string;
        /**
         * Required. The name of the project whose models are to be listed.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Models$Patch extends StandardParameters {
        /**
         * Required. The project name.
         */
        name?: string;
        /**
         * Required. Specifies the path, relative to `Model`, of the field to update. For example, to change the description of a model to "foo" and set its default version to "version_1", the `update_mask` parameter would be specified as `description`, `default_version.name`, and the `PATCH` request body would specify the new value, as follows: { "description": "foo", "defaultVersion": { "name":"version_1" \} \} Currently the supported update masks are `description` and `default_version.name`.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Model;
    }
    export interface Params$Resource$Projects$Models$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1__SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Models$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1__TestIamPermissionsRequest;
    }
    export class Resource$Projects$Models$Versions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new version of a model from a trained TensorFlow model. If the version created in the cloud by this call is the first deployed version of the specified model, it will be made the default version of the model. When you add a version to a model that already has one or more versions, the default version does not automatically change. If you want a new version to be the default, you must call projects.models.versions.setDefault.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Models$Versions$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Models$Versions$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        create(params: Params$Resource$Projects$Models$Versions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Models$Versions$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        create(params: Params$Resource$Projects$Models$Versions$Create, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Deletes a model version. Each model can have multiple versions deployed and in use at any given time. Use this method to remove a single version. Note: You cannot delete the version that is set as the default version of the model unless it is the only remaining version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Models$Versions$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Models$Versions$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        delete(params: Params$Resource$Projects$Models$Versions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Models$Versions$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        delete(params: Params$Resource$Projects$Models$Versions$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Gets information about a model version. Models can have multiple versions. You can call projects.models.versions.list to get the same information that this method returns for all of the versions of a model.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Models$Versions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Models$Versions$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Version>;
        get(params: Params$Resource$Projects$Models$Versions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Models$Versions$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Version>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
        get(params: Params$Resource$Projects$Models$Versions$Get, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
        /**
         * Gets basic information about all the versions of a model. If you expect that a model has many versions, or if you need to handle only a limited number of results at a time, you can request that the list be retrieved in batches (called pages). If there are no versions that match the request parameters, the list request returns an empty response body: {\}.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Models$Versions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Models$Versions$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__ListVersionsResponse>;
        list(params: Params$Resource$Projects$Models$Versions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Models$Versions$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__ListVersionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListVersionsResponse>): void;
        list(params: Params$Resource$Projects$Models$Versions$List, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__ListVersionsResponse>): void;
        /**
         * Updates the specified Version resource. Currently the only update-able fields are `description`, `requestLoggingConfig`, `autoScaling.minNodes`, and `manualScaling.nodes`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Models$Versions$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Models$Versions$Patch, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        patch(params: Params$Resource$Projects$Models$Versions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Models$Versions$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        patch(params: Params$Resource$Projects$Models$Versions$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Designates a version to be the default for the model. The default version is used for prediction requests made against the model that don't specify a version. The first version to be created for a model is automatically set as the default. You must make any subsequent changes to the default version setting manually using this method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setDefault(params: Params$Resource$Projects$Models$Versions$Setdefault, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setDefault(params?: Params$Resource$Projects$Models$Versions$Setdefault, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudMlV1__Version>;
        setDefault(params: Params$Resource$Projects$Models$Versions$Setdefault, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setDefault(params: Params$Resource$Projects$Models$Versions$Setdefault, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudMlV1__Version>, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
        setDefault(params: Params$Resource$Projects$Models$Versions$Setdefault, callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
        setDefault(callback: BodyResponseCallback<Schema$GoogleCloudMlV1__Version>): void;
    }
    export interface Params$Resource$Projects$Models$Versions$Create extends StandardParameters {
        /**
         * Required. The name of the model.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Version;
    }
    export interface Params$Resource$Projects$Models$Versions$Delete extends StandardParameters {
        /**
         * Required. The name of the version. You can get the names of all the versions of a model by calling projects.models.versions.list.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Models$Versions$Get extends StandardParameters {
        /**
         * Required. The name of the version.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Models$Versions$List extends StandardParameters {
        /**
         * Optional. Specifies the subset of versions to retrieve.
         */
        filter?: string;
        /**
         * Optional. The number of versions to retrieve per "page" of results. If there are more remaining results than this number, the response message will contain a valid value in the `next_page_token` field. The default value is 20, and the maximum page size is 100.
         */
        pageSize?: number;
        /**
         * Optional. A page token to request the next page of results. You get the token from the `next_page_token` field of the response from the previous call.
         */
        pageToken?: string;
        /**
         * Required. The name of the model for which to list the version.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Models$Versions$Patch extends StandardParameters {
        /**
         * Required. The name of the model.
         */
        name?: string;
        /**
         * Required. Specifies the path, relative to `Version`, of the field to update. Must be present and non-empty. For example, to change the description of a version to "foo", the `update_mask` parameter would be specified as `description`, and the `PATCH` request body would specify the new value, as follows: ``` { "description": "foo" \} ``` Currently the only supported update mask fields are `description`, `requestLoggingConfig`, `autoScaling.minNodes`, and `manualScaling.nodes`. However, you can only update `manualScaling.nodes` if the version uses a [Compute Engine (N1) machine type](/ml-engine/docs/machine-types-online-prediction).
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__Version;
    }
    export interface Params$Resource$Projects$Models$Versions$Setdefault extends StandardParameters {
        /**
         * Required. The name of the version to make the default for the model. You can get the names of all the versions of a model by calling projects.models.versions.list.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudMlV1__SetDefaultVersionRequest;
    }
    export class Resource$Projects$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobuf__Empty>;
        cancel(params: Params$Resource$Projects$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobuf__Empty>, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(params: Params$Resource$Projects$Operations$Cancel, callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleProtobuf__Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__Operation>;
        get(params: Params$Resource$Projects$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__Operation>, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        get(params: Params$Resource$Projects$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunning__Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunning__ListOperationsResponse>;
        list(params: Params$Resource$Projects$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunning__ListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunning__ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunning__ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunning__ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export {};
}
