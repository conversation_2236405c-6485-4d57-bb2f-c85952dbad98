{"version": 3, "file": "noopLogger.js", "sourceRoot": "", "sources": ["../../../../src/diag/internal/noopLogger.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,SAAS,eAAe,KAAI,CAAC;AAE7B;;;;GAIG;AACH,SAAgB,oBAAoB;IAClC,OAAO;QACL,OAAO,EAAE,eAAe;QACxB,KAAK,EAAE,eAAe;QACtB,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;KACvB,CAAC;AACJ,CAAC;AARD,oDAQC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogger } from '../types';\n\nfunction noopLogFunction() {}\n\n/**\n * Returns a No-Op Diagnostic logger where all messages do nothing.\n * @implements {@link DiagLogger}\n * @returns {DiagLogger}\n */\nexport function createNoopDiagLogger(): DiagLogger {\n  return {\n    verbose: noopLogFunction,\n    debug: noopLogFunction,\n    info: noopLogFunction,\n    warn: noopLogFunction,\n    error: noopLogFunction,\n  };\n}\n"]}