/*! firebase-admin v12.7.0 */
"use strict";
/*!
 * @license
 * Copyright 2017 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const firebase_namespace_1 = require("./app/firebase-namespace");
// Inject a circular default export to allow users to use both:
//
//   import firebaseAdmin from 'firebase-admin';
//   which becomes: var firebaseAdmin = require('firebase-admin').default;
//
// as well as the more correct:
//
//   import * as firebaseAdmin from 'firebase-admin';
//   which becomes: var firebaseAdmin = require('firebase-admin');
firebase_namespace_1.defaultNamespace.default = firebase_namespace_1.defaultNamespace;
module.exports = firebase_namespace_1.defaultNamespace;
