/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { datalineage_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof datalineage_v1.Datalineage;
};
export declare function datalineage(version: 'v1'): datalineage_v1.Datalineage;
export declare function datalineage(options: datalineage_v1.Options): datalineage_v1.Datalineage;
declare const auth: AuthPlus;
export { auth };
export { datalineage_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
