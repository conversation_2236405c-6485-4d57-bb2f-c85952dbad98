/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { chromepolicy_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof chromepolicy_v1.Chromepolicy;
};
export declare function chromepolicy(version: 'v1'): chromepolicy_v1.Chromepolicy;
export declare function chromepolicy(options: chromepolicy_v1.Options): chromepolicy_v1.Chromepolicy;
declare const auth: AuthPlus;
export { auth };
export { chromepolicy_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
