{"version": 3, "file": "streamArrayParser.js", "sourceRoot": "", "sources": ["../../src/streamArrayParser.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,uDAAwE;AACxE,mCAAiC;AAEjC,iDAA8C;AAC9C,yDAAsD;AAEtD,MAAa,iBAAkB,SAAQ,kBAAS;IAU9C;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,YAAY,GAAoB,EAAE,OAAY;QAC5C,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAC,kBAAkB,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,gBAAgB,GAAG,IAAA,qCAAkB,GAAE;YAC1C,CAAC,CAAC,IAAI,eAAe,EAAE;YACvB,CAAC,CAAC,IAAI,kCAAmB,EAAE,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAqB,CAAC;QAChE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,UAAU,CAAC,KAAa,EAAE,CAAS,EAAE,QAAkB;QACrD,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxC,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,CACP,OAAO,EACP,IAAI,KAAK,CACP,yHAAyH,MAAM,CAAC,YAAY,CAC1I,KAAK,CAAC,CAAC,CAAC,CACT,EAAE,CACJ,CACF,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;QAED,OAAO,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,QAAQ,QAAQ,EAAE,CAAC;oBACjB,KAAK,GAAG;wBACN,gEAAgE;wBAChE,gCAAgC;wBAChC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;4BACtB,IAAI,CAAC,MAAM,EAAE,CAAC;wBAChB,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC3C,WAAW,GAAG,QAAQ,CAAC;wBACzB,CAAC;wBACD,MAAM;oBACR,KAAK,GAAG;wBACN,yBAAyB;wBACzB,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;wBACrC,MAAM;oBACR,KAAK,GAAG;wBACN,0BAA0B;wBAC1B,sBAAsB;wBACtB,oCAAoC;wBACpC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;4BACtB,IAAI,CAAC,MAAM,EAAE,CAAC;wBAChB,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC3C,gBAAgB;4BAChB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gCAC5B,IAAI,CAAC,UAAU;gCACf,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG,CAAC,CAAC;6BACvC,CAAC,CAAC;4BACH,IAAI,CAAC;gCACH,4BAA4B;gCAC5B,MAAM,MAAM,GAAG,IAAA,6BAAc,EAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gCACvD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BACpB,CAAC;4BAAC,OAAO,GAAG,EAAE,CAAC;gCACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;4BAC1B,CAAC;4BACD,WAAW,GAAG,QAAQ,GAAG,CAAC,CAAC;4BAC3B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACpC,CAAC;wBACD,MAAM;oBACR,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;4BAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClB,CAAC;wBACD,MAAM;oBACR,KAAK,IAAI;wBACP,6BAA6B;wBAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;wBACvB,MAAM;oBACR;wBACE,MAAM;gBACV,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAC1B,CAAC;YACD,QAAQ,EAAE,CAAC;QACb,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,UAAU;gBACf,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC;aACnC,CAAC,CAAC;QACL,CAAC;QACD,QAAQ,EAAE,CAAC;IACb,CAAC;IAED,MAAM,CAAC,QAAkB;QACvB,QAAQ,EAAE,CAAC;IACb,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;CACF;AA/ID,8CA+IC"}