{"version": 3, "file": "longrunning.js", "sourceRoot": "", "sources": ["../../../src/longRunningCalls/longrunning.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAwWH,8BAOC;AA7WD,mCAAoC;AACpC,sCAAiC;AAKjC,gDAA2C;AAG3C,2DAA2D;AAoB3D,MAAa,SAAU,SAAQ,qBAAY;IAezC;;;;;;;;;;;;OAYG;IACH,YACE,MAAoB,EACpB,qBAA4C,EAC5C,eAAgC,EAChC,WAAyB;QAEzB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAA+B,CAAC;QACjE,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IAClC,CAAC;IAED;;;;;;;;;OASG;IACH,gBAAgB;QACd,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;YAC7B,IAAI,KAAK,KAAK,UAAU,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAChC,IAAI,KAAK,KAAK,UAAU,IAAI,EAAE,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM;QACJ,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;QACpC,CAAC;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;QACrE,MAAM,aAAa,GACjB,IAAI,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC;QAClE,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAC9C,OAAO,gBAAgB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAoBD,YAAY,CAAC,QAA+B;QAC1C,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;QAErE,SAAS,iBAAiB;YACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;wBAC9B,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAQ,CAAC,CAAC;wBAClE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAK,CAAC;wBAC7C,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YACpD,OAAO,iBAAiB,EAAiB,CAAC;QAC5C,CAAC;QACD,MAAM,OAAO,GACX,IAAI,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;QAC/D,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,gBAAgB,CAAC,oBAAoB,CAC9D,OAAO,EACP,IAAI,CAAC,YAAa,CACnB,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CACrD,SAAS,CAAC,EAAE;YACV,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC,CAAiB,CAAC;YACnD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAiB,EAAE,QAAQ,CAAC,CAAC;YAC7D,OAAO,iBAAiB,EAAG,CAAC;QAC9B,CAAC,EACD,CAAC,GAAU,EAAE,EAAE;YACb,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACd,OAAO;YACT,CAAC;YACD,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,iBAAgC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,eAAe,CAAC,EAAgB,EAAE,QAA+B;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;QACnE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;QACnE,IAAI,QAAY,CAAC;QACjB,IAAI,QAAkB,CAAC;QAEvB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,EAAE,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC1B,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,EAAE,CAAC,KAAM,CAAC,OAAQ,CAAC,CAAC;gBAClD,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,KAAM,CAAC,IAAK,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACnB,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,eAAe,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAC5B,QAAQ,GAAG,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;gBACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC;QACH,CAAC;QAED,IAAI,eAAe,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;YACnC,QAAQ,GAAG,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAM,CAAwB,CAAC;YACtE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,EAAE,QAAS,EAAE,QAAS,EAAE,EAAE,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,aAAa;QACX,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC;QACzD,IAAI,QAAQ,GAAG,QAAQ,CAAC;QACxB,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAC5C,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QACrE,CAAC;QACD,IAAI,qBAAiC,CAAC;QACtC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YACjC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAM,CAAC;QAC9D,CAAC;QAED,8DAA8D;QAC9D,SAAS,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;YAClD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5B,CAAC;QAED,sDAAsD;QACtD,SAAS,WAAW,CAAC,CAAa,EAAE,CAAa;YAC/C,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAAE,OAAO,KAAK,CAAC;YAClC,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,KAAK;YACZ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,QAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,IAAI,yBAAW,CAC3B,yDAAyD,CAC1D,CAAC;gBACF,KAAK,CAAC,IAAI,GAAG,eAAM,CAAC,iBAAiB,CAAC;gBACtC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE;gBACvD,IAAI,GAAG,EAAE,CAAC;oBACR,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;oBACjC,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,IACE,WAAY,CAAC,QAAQ;wBACrB,CAAC,CAAC,qBAAqB;4BACrB,CAAC,WAAW;gCACV,CAAC,WAAW,CACV,WAAW,CAAC,QAAQ,CAAC,KAAM,EAC3B,qBAAqB,CACtB,CAAC,CAAC,EACP,CAAC;wBACD,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;wBACtD,qBAAqB,GAAG,WAAY,CAAC,QAAS,CAAC,KAAM,CAAC;oBACxD,CAAC;oBACD,6DAA6D;oBAC7D,sDAAsD;oBACtD,oDAAoD;oBACpD,8DAA8D;oBAC9D,kCAAkC;oBAClC,IAAI,WAAY,CAAC,IAAI,EAAE,CAAC;wBACtB,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;wBAC1D,OAAO;oBACT,CAAC;oBACD,UAAU,CAAC,GAAG,EAAE;wBACd,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;wBACjB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;wBAC9C,KAAK,EAAE,CAAC;oBACV,CAAC,EAAE,KAAK,CAAC,CAAC;oBACV,OAAO;gBACT,CAAC;gBAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC;QACD,KAAK,EAAE,CAAC;IACV,CAAC;IAED;;;;;OAKG;IACH,OAAO;QACL,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,CACzB,UAAU,EACV,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE;gBAChC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;YAC3C,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA1TD,8BA0TC;AAED;;;;;;;;;;;;GAYG;AACH,SAAgB,SAAS,CACvB,EAAgB,EAChB,qBAA4C,EAC5C,eAAgC,EAChC,WAAyB;IAEzB,OAAO,IAAI,SAAS,CAAC,EAAE,EAAE,qBAAqB,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;AAChF,CAAC"}