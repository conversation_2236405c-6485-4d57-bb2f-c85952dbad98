import 'package:flutter/foundation.dart';
import 'dart:async';

/// Performance monitoring utility for image loading and app performance
class PerformanceMonitor {
  static final Map<String, DateTime> _loadingStartTimes = {};
  static final Map<String, Duration> _loadingDurations = {};
  static final List<String> _slowLoadingImages = [];
  
  /// Start monitoring image loading
  static void startImageLoading(String imageUrl) {
    if (kDebugMode) {
      _loadingStartTimes[imageUrl] = DateTime.now();
    }
  }
  
  /// End monitoring image loading
  static void endImageLoading(String imageUrl, {bool success = true}) {
    if (kDebugMode && _loadingStartTimes.containsKey(imageUrl)) {
      final startTime = _loadingStartTimes[imageUrl]!;
      final duration = DateTime.now().difference(startTime);
      _loadingDurations[imageUrl] = duration;
      
      // Log slow loading images (more than 3 seconds)
      if (duration.inSeconds > 3) {
        _slowLoadingImages.add(imageUrl);
        print('⚠️ Slow image loading detected: $imageUrl took ${duration.inSeconds}s');
      }
      
      // Log failed loading
      if (!success) {
        print('❌ Image loading failed: $imageUrl after ${duration.inSeconds}s');
      }
      
      _loadingStartTimes.remove(imageUrl);
    }
  }
  
  /// Get average loading time
  static Duration getAverageLoadingTime() {
    if (_loadingDurations.isEmpty) return Duration.zero;
    
    final totalMs = _loadingDurations.values
        .map((d) => d.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMs ~/ _loadingDurations.length);
  }
  
  /// Get slow loading images
  static List<String> getSlowLoadingImages() {
    return List.from(_slowLoadingImages);
  }
  
  /// Clear performance data
  static void clearData() {
    _loadingStartTimes.clear();
    _loadingDurations.clear();
    _slowLoadingImages.clear();
  }
  
  /// Get performance report
  static Map<String, dynamic> getPerformanceReport() {
    return {
      'total_images_loaded': _loadingDurations.length,
      'average_loading_time_ms': getAverageLoadingTime().inMilliseconds,
      'slow_loading_count': _slowLoadingImages.length,
      'slow_loading_images': _slowLoadingImages,
      'currently_loading': _loadingStartTimes.length,
    };
  }
  
  /// Log performance report
  static void logPerformanceReport() {
    if (kDebugMode) {
      final report = getPerformanceReport();
      print('📊 Image Loading Performance Report:');
      print('   Total images loaded: ${report['total_images_loaded']}');
      print('   Average loading time: ${report['average_loading_time_ms']}ms');
      print('   Slow loading images: ${report['slow_loading_count']}');
      print('   Currently loading: ${report['currently_loading']}');
    }
  }
  
  /// Monitor memory usage (placeholder for future implementation)
  static void monitorMemoryUsage() {
    if (kDebugMode) {
      // This would require platform-specific implementation
      // For now, just log that monitoring is active
      print('🔍 Memory monitoring active');
    }
  }
  
  /// Check if image URL is problematic
  static bool isProblematicUrl(String imageUrl) {
    return _slowLoadingImages.contains(imageUrl);
  }
  
  /// Get loading time for specific image
  static Duration? getLoadingTime(String imageUrl) {
    return _loadingDurations[imageUrl];
  }
  
  /// Start periodic performance logging
  static Timer? _performanceTimer;
  
  static void startPeriodicLogging({Duration interval = const Duration(minutes: 5)}) {
    if (kDebugMode) {
      _performanceTimer?.cancel();
      _performanceTimer = Timer.periodic(interval, (timer) {
        logPerformanceReport();
      });
    }
  }
  
  static void stopPeriodicLogging() {
    _performanceTimer?.cancel();
    _performanceTimer = null;
  }
}
