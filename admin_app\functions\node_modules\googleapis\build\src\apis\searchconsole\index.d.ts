/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { searchconsole_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof searchconsole_v1.Searchconsole;
};
export declare function searchconsole(version: 'v1'): searchconsole_v1.Searchconsole;
export declare function searchconsole(options: searchconsole_v1.Options): searchconsole_v1.Searchconsole;
declare const auth: AuthPlus;
export { auth };
export { searchconsole_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
