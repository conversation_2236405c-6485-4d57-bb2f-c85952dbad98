/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace run_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Run Admin API
     *
     * Deploy and manage user provided container images that scale automatically based on incoming requests. The Cloud Run Admin API v1 follows the Knative Serving API specification, while v2 is aligned with Google Cloud AIP-based API standards, as described in https://google.aip.dev/.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const run = google.run('v2');
     * ```
     */
    export class Run {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Settings for Binary Authorization feature.
     */
    export interface Schema$GoogleCloudRunV2BinaryAuthorization {
        /**
         * If present, indicates to use Breakglass using this justification. If use_default is False, then it must be empty. For more information on breakglass, see https://cloud.google.com/binary-authorization/docs/using-breakglass
         */
        breakglassJustification?: string | null;
        /**
         * If True, indicates to use the default project's binary authorization policy. If False, binary authorization will be disabled.
         */
        useDefault?: boolean | null;
    }
    /**
     * Request message for deleting an Execution.
     */
    export interface Schema$GoogleCloudRunV2CancelExecutionRequest {
        /**
         * A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Indicates that the request should be validated without actually cancelling any resources.
         */
        validateOnly?: boolean | null;
    }
    /**
     * Represents a set of Cloud SQL instances. Each one will be available under /cloudsql/[instance]. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run.
     */
    export interface Schema$GoogleCloudRunV2CloudSqlInstance {
        /**
         * The Cloud SQL instance connection names, as can be found in https://console.cloud.google.com/sql/instances. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run. Format: {project\}:{location\}:{instance\}
         */
        instances?: string[] | null;
    }
    /**
     * Defines a status condition for a resource.
     */
    export interface Schema$GoogleCloudRunV2Condition {
        /**
         * A reason for the execution condition.
         */
        executionReason?: string | null;
        /**
         * Last time the condition transitioned from one status to another.
         */
        lastTransitionTime?: string | null;
        /**
         * Human readable message indicating details about the current status.
         */
        message?: string | null;
        /**
         * A common (service-level) reason for this condition.
         */
        reason?: string | null;
        /**
         * A reason for the revision condition.
         */
        revisionReason?: string | null;
        /**
         * How to interpret failures of this condition, one of Error, Warning, Info
         */
        severity?: string | null;
        /**
         * State of the condition.
         */
        state?: string | null;
        /**
         * type is used to communicate the status of the reconciliation process. See also: https://github.com/knative/serving/blob/main/docs/spec/errors.md#error-conditions-and-reporting Types common to all resources include: * "Ready": True when the Resource is ready.
         */
        type?: string | null;
    }
    /**
     * A single application container. This specifies both the container to run, the command to run in the container and the arguments to supply to it. Note that additional arguments can be supplied by the system to the container at runtime.
     */
    export interface Schema$GoogleCloudRunV2Container {
        /**
         * Arguments to the entrypoint. The docker image's CMD is used if this is not provided.
         */
        args?: string[] | null;
        /**
         * Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided.
         */
        command?: string[] | null;
        /**
         * Names of the containers that must start before this container.
         */
        dependsOn?: string[] | null;
        /**
         * List of environment variables to set in the container.
         */
        env?: Schema$GoogleCloudRunV2EnvVar[];
        /**
         * Required. Name of the container image in Dockerhub, Google Artifact Registry, or Google Container Registry. If the host is not provided, Dockerhub is assumed.
         */
        image?: string | null;
        /**
         * Periodic probe of container liveness. Container will be restarted if the probe fails.
         */
        livenessProbe?: Schema$GoogleCloudRunV2Probe;
        /**
         * Name of the container specified as a DNS_LABEL (RFC 1123).
         */
        name?: string | null;
        /**
         * List of ports to expose from the container. Only a single port can be specified. The specified ports must be listening on all interfaces (0.0.0.0) within the container to be accessible. If omitted, a port number will be chosen and passed to the container through the PORT environment variable for the container to listen on.
         */
        ports?: Schema$GoogleCloudRunV2ContainerPort[];
        /**
         * Compute Resource requirements by this container.
         */
        resources?: Schema$GoogleCloudRunV2ResourceRequirements;
        /**
         * Startup probe of application within the container. All other probes are disabled if a startup probe is provided, until it succeeds. Container will not be added to service endpoints if the probe fails.
         */
        startupProbe?: Schema$GoogleCloudRunV2Probe;
        /**
         * Volume to mount into the container's filesystem.
         */
        volumeMounts?: Schema$GoogleCloudRunV2VolumeMount[];
        /**
         * Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image.
         */
        workingDir?: string | null;
    }
    /**
     * Per-container override specification.
     */
    export interface Schema$GoogleCloudRunV2ContainerOverride {
        /**
         * Optional. Arguments to the entrypoint. Will replace existing args for override.
         */
        args?: string[] | null;
        /**
         * Optional. True if the intention is to clear out existing args list.
         */
        clearArgs?: boolean | null;
        /**
         * List of environment variables to set in the container. Will be merged with existing env for override.
         */
        env?: Schema$GoogleCloudRunV2EnvVar[];
        /**
         * The name of the container specified as a DNS_LABEL.
         */
        name?: string | null;
    }
    /**
     * ContainerPort represents a network port in a single container.
     */
    export interface Schema$GoogleCloudRunV2ContainerPort {
        /**
         * Port number the container listens on. This must be a valid TCP port number, 0 < container_port < 65536.
         */
        containerPort?: number | null;
        /**
         * If specified, used to specify which protocol to use. Allowed values are "http1" and "h2c".
         */
        name?: string | null;
    }
    /**
     * In memory (tmpfs) ephemeral storage. It is ephemeral in the sense that when the sandbox is taken down, the data is destroyed with it (it does not persist across sandbox runs).
     */
    export interface Schema$GoogleCloudRunV2EmptyDirVolumeSource {
        /**
         * The medium on which the data is stored. Acceptable values today is only MEMORY or none. When none, the default will currently be backed by memory but could change over time. +optional
         */
        medium?: string | null;
        /**
         * Limit on the storage usable by this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers. The default is nil which means that the limit is undefined. More info: https://cloud.google.com/run/docs/configuring/in-memory-volumes#configure-volume. Info in Kubernetes: https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
         */
        sizeLimit?: string | null;
    }
    /**
     * EnvVar represents an environment variable present in a Container.
     */
    export interface Schema$GoogleCloudRunV2EnvVar {
        /**
         * Required. Name of the environment variable. Must not exceed 32768 characters.
         */
        name?: string | null;
        /**
         * Variable references $(VAR_NAME) are expanded using the previous defined environment variables in the container and any route environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to "", and the maximum length is 32768 bytes.
         */
        value?: string | null;
        /**
         * Source for the environment variable's value.
         */
        valueSource?: Schema$GoogleCloudRunV2EnvVarSource;
    }
    /**
     * EnvVarSource represents a source for the value of an EnvVar.
     */
    export interface Schema$GoogleCloudRunV2EnvVarSource {
        /**
         * Selects a secret and a specific version from Cloud Secret Manager.
         */
        secretKeyRef?: Schema$GoogleCloudRunV2SecretKeySelector;
    }
    /**
     * Execution represents the configuration of a single execution. A execution an immutable resource that references a container image which is run to completion.
     */
    export interface Schema$GoogleCloudRunV2Execution {
        /**
         * Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The number of tasks which reached phase Cancelled.
         */
        cancelledCount?: number | null;
        /**
         * Output only. Represents time when the execution was completed. It is not guaranteed to be set in happens-before order across separate operations.
         */
        completionTime?: string | null;
        /**
         * Output only. The Condition of this Execution, containing its readiness status, and detailed error information in case it did not reach the desired state.
         */
        conditions?: Schema$GoogleCloudRunV2Condition[];
        /**
         * Output only. Represents time when the execution was acknowledged by the execution controller. It is not guaranteed to be set in happens-before order across separate operations.
         */
        createTime?: string | null;
        /**
         * Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.
         */
        deleteTime?: string | null;
        /**
         * Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.
         */
        expireTime?: string | null;
        /**
         * Output only. The number of tasks which reached phase Failed.
         */
        failedCount?: number | null;
        /**
         * Output only. A number that monotonically increases every time the user modifies the desired state.
         */
        generation?: string | null;
        /**
         * Output only. The name of the parent Job.
         */
        job?: string | null;
        /**
         * Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The least stable launch stage needed to create this resource, as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used as input. For example, if ALPHA was provided as input in the parent resource, but only BETA and GA-level features are were, this field will be BETA.
         */
        launchStage?: string | null;
        /**
         * Output only. URI where logs for this execution can be found in Cloud Console.
         */
        logUri?: string | null;
        /**
         * Output only. The unique name of this Execution.
         */
        name?: string | null;
        /**
         * Output only. The generation of this Execution. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        observedGeneration?: string | null;
        /**
         * Output only. Specifies the maximum desired number of tasks the execution should run at any given time. Must be <= task_count. The actual number of tasks running in steady state will be less than this number when ((.spec.task_count - .status.successful) < .spec.parallelism), i.e. when the work left to do is less than max parallelism.
         */
        parallelism?: number | null;
        /**
         * Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The number of tasks which have retried at least once.
         */
        retriedCount?: number | null;
        /**
         * Output only. The number of actively running tasks.
         */
        runningCount?: number | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. Represents time when the execution started to run. It is not guaranteed to be set in happens-before order across separate operations.
         */
        startTime?: string | null;
        /**
         * Output only. The number of tasks which reached phase Succeeded.
         */
        succeededCount?: number | null;
        /**
         * Output only. Specifies the desired number of tasks the execution should run. Setting to 1 means that parallelism is limited to 1 and the success of that task signals the success of the execution.
         */
        taskCount?: number | null;
        /**
         * Output only. The template used to create tasks for this execution.
         */
        template?: Schema$GoogleCloudRunV2TaskTemplate;
        /**
         * Output only. Server assigned unique identifier for the Execution. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.
         */
        uid?: string | null;
        /**
         * Output only. The last-modified time.
         */
        updateTime?: string | null;
    }
    /**
     * Reference to an Execution. Use /Executions.GetExecution with the given name to get full execution including the latest status.
     */
    export interface Schema$GoogleCloudRunV2ExecutionReference {
        /**
         * Creation timestamp of the execution.
         */
        completionTime?: string | null;
        /**
         * Creation timestamp of the execution.
         */
        createTime?: string | null;
        /**
         * Name of the execution.
         */
        name?: string | null;
    }
    /**
     * ExecutionTemplate describes the data an execution should have when created from a template.
     */
    export interface Schema$GoogleCloudRunV2ExecutionTemplate {
        /**
         * Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system annotations in v1 now have a corresponding field in v2 ExecutionTemplate. This field follows Kubernetes annotations' namespacing, limits, and rules.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 ExecutionTemplate.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Specifies the maximum desired number of tasks the execution should run at given time. Must be <= task_count. When the job is run, if this field is 0 or unset, the maximum possible value will be used for that execution. The actual number of tasks running in steady state will be less than this number when there are fewer tasks waiting to be completed remaining, i.e. when the work left to do is less than max parallelism.
         */
        parallelism?: number | null;
        /**
         * Specifies the desired number of tasks the execution should run. Setting to 1 means that parallelism is limited to 1 and the success of that task signals the success of the execution. Defaults to 1.
         */
        taskCount?: number | null;
        /**
         * Required. Describes the task(s) that will be created when executing an execution.
         */
        template?: Schema$GoogleCloudRunV2TaskTemplate;
    }
    /**
     * GRPCAction describes an action involving a GRPC port.
     */
    export interface Schema$GoogleCloudRunV2GRPCAction {
        /**
         * Port number of the gRPC service. Number must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.
         */
        port?: number | null;
        /**
         * Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md ). If this is not specified, the default behavior is defined by gRPC.
         */
        service?: string | null;
    }
    /**
     * HTTPGetAction describes an action based on HTTP Get requests.
     */
    export interface Schema$GoogleCloudRunV2HTTPGetAction {
        /**
         * Custom headers to set in the request. HTTP allows repeated headers.
         */
        httpHeaders?: Schema$GoogleCloudRunV2HTTPHeader[];
        /**
         * Path to access on the HTTP server. Defaults to '/'.
         */
        path?: string | null;
        /**
         * Port number to access on the container. Must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.
         */
        port?: number | null;
    }
    /**
     * HTTPHeader describes a custom header to be used in HTTP probes
     */
    export interface Schema$GoogleCloudRunV2HTTPHeader {
        /**
         * Required. The header field name
         */
        name?: string | null;
        /**
         * The header field value
         */
        value?: string | null;
    }
    /**
     * Job represents the configuration of a single job, which references a container image that is run to completion.
     */
    export interface Schema$GoogleCloudRunV2Job {
        /**
         * Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected on new resources. All system annotations in v1 now have a corresponding field in v2 Job. This field follows Kubernetes annotations' namespacing, limits, and rules.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Settings for the Binary Authorization feature.
         */
        binaryAuthorization?: Schema$GoogleCloudRunV2BinaryAuthorization;
        /**
         * Arbitrary identifier for the API client.
         */
        client?: string | null;
        /**
         * Arbitrary version identifier for the API client.
         */
        clientVersion?: string | null;
        /**
         * Output only. The Conditions of all other associated sub-resources. They contain additional diagnostics information in case the Job does not reach its desired state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        conditions?: Schema$GoogleCloudRunV2Condition[];
        /**
         * Output only. The creation time.
         */
        createTime?: string | null;
        /**
         * Output only. Email address of the authenticated creator.
         */
        creator?: string | null;
        /**
         * Output only. The deletion time.
         */
        deleteTime?: string | null;
        /**
         * Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Output only. Number of executions created for this job.
         */
        executionCount?: number | null;
        /**
         * Output only. For a deleted resource, the time after which it will be permamently deleted.
         */
        expireTime?: string | null;
        /**
         * Output only. A number that monotonically increases every time the user modifies the desired state.
         */
        generation?: string | null;
        /**
         * Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 Job.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Email address of the last authenticated modifier.
         */
        lastModifier?: string | null;
        /**
         * Output only. Name of the last created execution.
         */
        latestCreatedExecution?: Schema$GoogleCloudRunV2ExecutionReference;
        /**
         * The launch stage as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set the launch stage to a preview stage on input to allow use of preview features in that stage. On read (or output), describes whether the resource uses preview features. For example, if ALPHA is provided as input, but only BETA and GA-level features are used, this field will be BETA on output.
         */
        launchStage?: string | null;
        /**
         * The fully qualified name of this Job. Format: projects/{project\}/locations/{location\}/jobs/{job\}
         */
        name?: string | null;
        /**
         * Output only. The generation of this Job. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        observedGeneration?: string | null;
        /**
         * Output only. Returns true if the Job is currently being acted upon by the system to bring it into the desired state. When a new Job is created, or an existing one is updated, Cloud Run will asynchronously perform all necessary steps to bring the Job to the desired state. This process is called reconciliation. While reconciliation is in process, `observed_generation` and `latest_succeeded_execution`, will have transient values that might mismatch the intended state: Once reconciliation is over (and this field is false), there are two possible outcomes: reconciliation succeeded and the state matches the Job, or there was an error, and reconciliation failed. This state can be found in `terminal_condition.state`. If reconciliation succeeded, the following fields will match: `observed_generation` and `generation`, `latest_succeeded_execution` and `latest_created_execution`. If reconciliation failed, `observed_generation` and `latest_succeeded_execution` will have the state of the last succeeded execution or empty for newly created Job. Additional information on the failure can be found in `terminal_condition` and `conditions`.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Required. The template used to create executions for this Job.
         */
        template?: Schema$GoogleCloudRunV2ExecutionTemplate;
        /**
         * Output only. The Condition of this Job, containing its readiness status, and detailed error information in case it did not reach the desired state.
         */
        terminalCondition?: Schema$GoogleCloudRunV2Condition;
        /**
         * Output only. Server assigned unique identifier for the Execution. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.
         */
        uid?: string | null;
        /**
         * Output only. The last-modified time.
         */
        updateTime?: string | null;
    }
    /**
     * Response message containing a list of Executions.
     */
    export interface Schema$GoogleCloudRunV2ListExecutionsResponse {
        /**
         * The resulting list of Executions.
         */
        executions?: Schema$GoogleCloudRunV2Execution[];
        /**
         * A token indicating there are more items than page_size. Use it in the next ListExecutions request to continue.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message containing a list of Jobs.
     */
    export interface Schema$GoogleCloudRunV2ListJobsResponse {
        /**
         * The resulting list of Jobs.
         */
        jobs?: Schema$GoogleCloudRunV2Job[];
        /**
         * A token indicating there are more items than page_size. Use it in the next ListJobs request to continue.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message containing a list of Revisions.
     */
    export interface Schema$GoogleCloudRunV2ListRevisionsResponse {
        /**
         * A token indicating there are more items than page_size. Use it in the next ListRevisions request to continue.
         */
        nextPageToken?: string | null;
        /**
         * The resulting list of Revisions.
         */
        revisions?: Schema$GoogleCloudRunV2Revision[];
    }
    /**
     * Response message containing a list of Services.
     */
    export interface Schema$GoogleCloudRunV2ListServicesResponse {
        /**
         * A token indicating there are more items than page_size. Use it in the next ListServices request to continue.
         */
        nextPageToken?: string | null;
        /**
         * The resulting list of Services.
         */
        services?: Schema$GoogleCloudRunV2Service[];
    }
    /**
     * Response message containing a list of Tasks.
     */
    export interface Schema$GoogleCloudRunV2ListTasksResponse {
        /**
         * A token indicating there are more items than page_size. Use it in the next ListTasks request to continue.
         */
        nextPageToken?: string | null;
        /**
         * The resulting list of Tasks.
         */
        tasks?: Schema$GoogleCloudRunV2Task[];
    }
    /**
     * Direct VPC egress settings.
     */
    export interface Schema$GoogleCloudRunV2NetworkInterface {
        /**
         * The VPC network that the Cloud Run resource will be able to send traffic to. At least one of network or subnetwork must be specified. If both network and subnetwork are specified, the given VPC subnetwork must belong to the given VPC network. If network is not specified, it will be looked up from the subnetwork.
         */
        network?: string | null;
        /**
         * The VPC subnetwork that the Cloud Run resource will get IPs from. At least one of network or subnetwork must be specified. If both network and subnetwork are specified, the given VPC subnetwork must belong to the given VPC network. If subnetwork is not specified, the subnetwork with the same name with the network will be used.
         */
        subnetwork?: string | null;
        /**
         * Network tags applied to this Cloud Run resource.
         */
        tags?: string[] | null;
    }
    /**
     * RunJob Overrides that contains Execution fields to be overridden.
     */
    export interface Schema$GoogleCloudRunV2Overrides {
        /**
         * Per container override specification.
         */
        containerOverrides?: Schema$GoogleCloudRunV2ContainerOverride[];
        /**
         * Optional. The desired number of tasks the execution should run. Will replace existing task_count value.
         */
        taskCount?: number | null;
        /**
         * Duration in seconds the task may be active before the system will actively try to mark it failed and kill associated containers. Will replace existing timeout_seconds value.
         */
        timeout?: string | null;
    }
    /**
     * Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.
     */
    export interface Schema$GoogleCloudRunV2Probe {
        /**
         * Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.
         */
        failureThreshold?: number | null;
        /**
         * GRPC specifies an action involving a gRPC port. Exactly one of httpGet, tcpSocket, or grpc must be specified.
         */
        grpc?: Schema$GoogleCloudRunV2GRPCAction;
        /**
         * HTTPGet specifies the http request to perform. Exactly one of httpGet, tcpSocket, or grpc must be specified.
         */
        httpGet?: Schema$GoogleCloudRunV2HTTPGetAction;
        /**
         * Number of seconds after the container has started before the probe is initiated. Defaults to 0 seconds. Minimum value is 0. Maximum value for liveness probe is 3600. Maximum value for startup probe is 240.
         */
        initialDelaySeconds?: number | null;
        /**
         * How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1. Maximum value for liveness probe is 3600. Maximum value for startup probe is 240. Must be greater or equal than timeout_seconds.
         */
        periodSeconds?: number | null;
        /**
         * TCPSocket specifies an action involving a TCP port. Exactly one of httpGet, tcpSocket, or grpc must be specified.
         */
        tcpSocket?: Schema$GoogleCloudRunV2TCPSocketAction;
        /**
         * Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. Maximum value is 3600. Must be smaller than period_seconds.
         */
        timeoutSeconds?: number | null;
    }
    /**
     * ResourceRequirements describes the compute resource requirements.
     */
    export interface Schema$GoogleCloudRunV2ResourceRequirements {
        /**
         * Determines whether CPU should be throttled or not outside of requests.
         */
        cpuIdle?: boolean | null;
        /**
         * Only ´memory´ and 'cpu' are supported. Notes: * The only supported values for CPU are '1', '2', '4', and '8'. Setting 4 CPU requires at least 2Gi of memory. For more information, go to https://cloud.google.com/run/docs/configuring/cpu. * For supported 'memory' values and syntax, go to https://cloud.google.com/run/docs/configuring/memory-limits
         */
        limits?: {
            [key: string]: string;
        } | null;
        /**
         * Determines whether CPU should be boosted on startup of a new container instance above the requested CPU threshold, this can help reduce cold-start latency.
         */
        startupCpuBoost?: boolean | null;
    }
    /**
     * A Revision is an immutable snapshot of code and configuration. A Revision references a container image. Revisions are only created by updates to its parent Service.
     */
    export interface Schema$GoogleCloudRunV2Revision {
        /**
         * Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The Condition of this Revision, containing its readiness status, and detailed error information in case it did not reach a serving state.
         */
        conditions?: Schema$GoogleCloudRunV2Condition[];
        /**
         * Holds the single container that defines the unit of execution for this Revision.
         */
        containers?: Schema$GoogleCloudRunV2Container[];
        /**
         * Output only. The creation time.
         */
        createTime?: string | null;
        /**
         * Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.
         */
        deleteTime?: string | null;
        /**
         * A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek
         */
        encryptionKey?: string | null;
        /**
         * The action to take if the encryption key is revoked.
         */
        encryptionKeyRevocationAction?: string | null;
        /**
         * If encryption_key_revocation_action is SHUTDOWN, the duration before shutting down all instances. The minimum increment is 1 hour.
         */
        encryptionKeyShutdownDuration?: string | null;
        /**
         * Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * The execution environment being used to host this Revision.
         */
        executionEnvironment?: string | null;
        /**
         * Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.
         */
        expireTime?: string | null;
        /**
         * Output only. A number that monotonically increases every time the user modifies the desired state.
         */
        generation?: string | null;
        /**
         * Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The least stable launch stage needed to create this resource, as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used as input. For example, if ALPHA was provided as input in the parent resource, but only BETA and GA-level features are were, this field will be BETA.
         */
        launchStage?: string | null;
        /**
         * Output only. The Google Console URI to obtain logs for the Revision.
         */
        logUri?: string | null;
        /**
         * Sets the maximum number of requests that each serving instance can receive.
         */
        maxInstanceRequestConcurrency?: number | null;
        /**
         * Output only. The unique name of this Revision.
         */
        name?: string | null;
        /**
         * Output only. The generation of this Revision currently serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        observedGeneration?: string | null;
        /**
         * Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Service.reconciling` for additional information on reconciliation process in Cloud Run.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Scaling settings for this revision.
         */
        scaling?: Schema$GoogleCloudRunV2RevisionScaling;
        /**
         * Output only. The current effective scaling settings for the revision.
         */
        scalingStatus?: Schema$GoogleCloudRunV2RevisionScalingStatus;
        /**
         * Output only. The name of the parent service.
         */
        service?: string | null;
        /**
         * Email address of the IAM service account associated with the revision of the service. The service account represents the identity of the running revision, and determines what permissions the revision has.
         */
        serviceAccount?: string | null;
        /**
         * Enable session affinity.
         */
        sessionAffinity?: boolean | null;
        /**
         * Max allowed time for an instance to respond to a request.
         */
        timeout?: string | null;
        /**
         * Output only. Server assigned unique identifier for the Revision. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.
         */
        uid?: string | null;
        /**
         * Output only. The last-modified time.
         */
        updateTime?: string | null;
        /**
         * A list of Volumes to make available to containers.
         */
        volumes?: Schema$GoogleCloudRunV2Volume[];
        /**
         * VPC Access configuration for this Revision. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
         */
        vpcAccess?: Schema$GoogleCloudRunV2VpcAccess;
    }
    /**
     * Settings for revision-level scaling settings.
     */
    export interface Schema$GoogleCloudRunV2RevisionScaling {
        /**
         * Maximum number of serving instances that this resource should have.
         */
        maxInstanceCount?: number | null;
        /**
         * Minimum number of serving instances that this resource should have.
         */
        minInstanceCount?: number | null;
    }
    /**
     * Effective settings for the current revision
     */
    export interface Schema$GoogleCloudRunV2RevisionScalingStatus {
        /**
         * The current number of min instances provisioned for this revision.
         */
        desiredMinInstanceCount?: number | null;
    }
    /**
     * RevisionTemplate describes the data a revision should have when created from a template.
     */
    export interface Schema$GoogleCloudRunV2RevisionTemplate {
        /**
         * Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system annotations in v1 now have a corresponding field in v2 RevisionTemplate. This field follows Kubernetes annotations' namespacing, limits, and rules.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Holds the single container that defines the unit of execution for this Revision.
         */
        containers?: Schema$GoogleCloudRunV2Container[];
        /**
         * A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek
         */
        encryptionKey?: string | null;
        /**
         * The sandbox environment to host this Revision.
         */
        executionEnvironment?: string | null;
        /**
         * Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 RevisionTemplate.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Sets the maximum number of requests that each serving instance can receive.
         */
        maxInstanceRequestConcurrency?: number | null;
        /**
         * The unique name for the revision. If this field is omitted, it will be automatically generated based on the Service name.
         */
        revision?: string | null;
        /**
         * Scaling settings for this Revision.
         */
        scaling?: Schema$GoogleCloudRunV2RevisionScaling;
        /**
         * Email address of the IAM service account associated with the revision of the service. The service account represents the identity of the running revision, and determines what permissions the revision has. If not provided, the revision will use the project's default service account.
         */
        serviceAccount?: string | null;
        /**
         * Enable session affinity.
         */
        sessionAffinity?: boolean | null;
        /**
         * Max allowed time for an instance to respond to a request.
         */
        timeout?: string | null;
        /**
         * A list of Volumes to make available to containers.
         */
        volumes?: Schema$GoogleCloudRunV2Volume[];
        /**
         * VPC Access configuration to use for this Revision. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
         */
        vpcAccess?: Schema$GoogleCloudRunV2VpcAccess;
    }
    /**
     * Request message to create a new Execution of a Job.
     */
    export interface Schema$GoogleCloudRunV2RunJobRequest {
        /**
         * A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Overrides specification for a given execution of a job. If provided, overrides will be applied to update the execution or task spec.
         */
        overrides?: Schema$GoogleCloudRunV2Overrides;
        /**
         * Indicates that the request should be validated without actually deleting any resources.
         */
        validateOnly?: boolean | null;
    }
    /**
     * SecretEnvVarSource represents a source for the value of an EnvVar.
     */
    export interface Schema$GoogleCloudRunV2SecretKeySelector {
        /**
         * Required. The name of the secret in Cloud Secret Manager. Format: {secret_name\} if the secret is in the same project. projects/{project\}/secrets/{secret_name\} if the secret is in a different project.
         */
        secret?: string | null;
        /**
         * The Cloud Secret Manager secret version. Can be 'latest' for the latest version, an integer for a specific version, or a version alias.
         */
        version?: string | null;
    }
    /**
     * The secret's value will be presented as the content of a file whose name is defined in the item path. If no items are defined, the name of the file is the secret.
     */
    export interface Schema$GoogleCloudRunV2SecretVolumeSource {
        /**
         * Integer representation of mode bits to use on created files by default. Must be a value between 0000 and 0777 (octal), defaulting to 0444. Directories within the path are not affected by this setting. Notes * Internally, a umask of 0222 will be applied to any non-zero value. * This is an integer representation of the mode bits. So, the octal integer value should look exactly as the chmod numeric notation with a leading zero. Some examples: for chmod 777 (a=rwx), set to 0777 (octal) or 511 (base-10). For chmod 640 (u=rw,g=r), set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or 493 (base-10). * This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set. This might be in conflict with other options that affect the file mode, like fsGroup, and as a result, other mode bits could be set.
         */
        defaultMode?: number | null;
        /**
         * If unspecified, the volume will expose a file whose name is the secret, relative to VolumeMount.mount_path. If specified, the key will be used as the version to fetch from Cloud Secret Manager and the path will be the name of the file exposed in the volume. When items are defined, they must specify a path and a version.
         */
        items?: Schema$GoogleCloudRunV2VersionToPath[];
        /**
         * Required. The name of the secret in Cloud Secret Manager. Format: {secret\} if the secret is in the same project. projects/{project\}/secrets/{secret\} if the secret is in a different project.
         */
        secret?: string | null;
    }
    /**
     * Service acts as a top-level container that manages a set of configurations and revision templates which implement a network service. Service exists to provide a singular abstraction which can be access controlled, reasoned about, and which encapsulates software lifecycle decisions such as rollout policy and team resource ownership.
     */
    export interface Schema$GoogleCloudRunV2Service {
        /**
         * Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected in new resources. All system annotations in v1 now have a corresponding field in v2 Service. This field follows Kubernetes annotations' namespacing, limits, and rules.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Settings for the Binary Authorization feature.
         */
        binaryAuthorization?: Schema$GoogleCloudRunV2BinaryAuthorization;
        /**
         * Arbitrary identifier for the API client.
         */
        client?: string | null;
        /**
         * Arbitrary version identifier for the API client.
         */
        clientVersion?: string | null;
        /**
         * Output only. The Conditions of all other associated sub-resources. They contain additional diagnostics information in case the Service does not reach its Serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        conditions?: Schema$GoogleCloudRunV2Condition[];
        /**
         * Output only. The creation time.
         */
        createTime?: string | null;
        /**
         * Output only. Email address of the authenticated creator.
         */
        creator?: string | null;
        /**
         * One or more custom audiences that you want this service to support. Specify each custom audience as the full URL in a string. The custom audiences are encoded in the token and used to authenticate requests. For more information, see https://cloud.google.com/run/docs/configuring/custom-audiences.
         */
        customAudiences?: string[] | null;
        /**
         * Output only. The deletion time.
         */
        deleteTime?: string | null;
        /**
         * User-provided description of the Service. This field currently has a 512-character limit.
         */
        description?: string | null;
        /**
         * Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Output only. For a deleted resource, the time after which it will be permamently deleted.
         */
        expireTime?: string | null;
        /**
         * Output only. A number that monotonically increases every time the user modifies the desired state. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.
         */
        generation?: string | null;
        /**
         * Provides the ingress settings for this Service. On output, returns the currently observed ingress settings, or INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.
         */
        ingress?: string | null;
        /**
         * Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 Service.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Email address of the last authenticated modifier.
         */
        lastModifier?: string | null;
        /**
         * Output only. Name of the last created revision. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        latestCreatedRevision?: string | null;
        /**
         * Output only. Name of the latest revision that is serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        latestReadyRevision?: string | null;
        /**
         * The launch stage as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set the launch stage to a preview stage on input to allow use of preview features in that stage. On read (or output), describes whether the resource uses preview features. For example, if ALPHA is provided as input, but only BETA and GA-level features are used, this field will be BETA on output.
         */
        launchStage?: string | null;
        /**
         * The fully qualified name of this Service. In CreateServiceRequest, this field is ignored, and instead composed from CreateServiceRequest.parent and CreateServiceRequest.service_id. Format: projects/{project\}/locations/{location\}/services/{service_id\}
         */
        name?: string | null;
        /**
         * Output only. The generation of this Service currently serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.
         */
        observedGeneration?: string | null;
        /**
         * Output only. Returns true if the Service is currently being acted upon by the system to bring it into the desired state. When a new Service is created, or an existing one is updated, Cloud Run will asynchronously perform all necessary steps to bring the Service to the desired serving state. This process is called reconciliation. While reconciliation is in process, `observed_generation`, `latest_ready_revison`, `traffic_statuses`, and `uri` will have transient values that might mismatch the intended state: Once reconciliation is over (and this field is false), there are two possible outcomes: reconciliation succeeded and the serving state matches the Service, or there was an error, and reconciliation failed. This state can be found in `terminal_condition.state`. If reconciliation succeeded, the following fields will match: `traffic` and `traffic_statuses`, `observed_generation` and `generation`, `latest_ready_revision` and `latest_created_revision`. If reconciliation failed, `traffic_statuses`, `observed_generation`, and `latest_ready_revision` will have the state of the last serving revision, or empty for newly created Services. Additional information on the failure can be found in `terminal_condition` and `conditions`.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Required. The template used to create revisions for this Service.
         */
        template?: Schema$GoogleCloudRunV2RevisionTemplate;
        /**
         * Output only. The Condition of this Service, containing its readiness status, and detailed error information in case it did not reach a serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        terminalCondition?: Schema$GoogleCloudRunV2Condition;
        /**
         * Specifies how to distribute traffic over a collection of Revisions belonging to the Service. If traffic is empty or not provided, defaults to 100% traffic to the latest `Ready` Revision.
         */
        traffic?: Schema$GoogleCloudRunV2TrafficTarget[];
        /**
         * Output only. Detailed status information for corresponding traffic targets. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.
         */
        trafficStatuses?: Schema$GoogleCloudRunV2TrafficTargetStatus[];
        /**
         * Output only. Server assigned unique identifier for the trigger. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.
         */
        uid?: string | null;
        /**
         * Output only. The last-modified time.
         */
        updateTime?: string | null;
        /**
         * Output only. The main URI in which this Service is serving traffic.
         */
        uri?: string | null;
    }
    /**
     * Task represents a single run of a container to completion.
     */
    export interface Schema$GoogleCloudRunV2Task {
        /**
         * Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Represents time when the Task was completed. It is not guaranteed to be set in happens-before order across separate operations.
         */
        completionTime?: string | null;
        /**
         * Output only. The Condition of this Task, containing its readiness status, and detailed error information in case it did not reach the desired state.
         */
        conditions?: Schema$GoogleCloudRunV2Condition[];
        /**
         * Holds the single container that defines the unit of execution for this task.
         */
        containers?: Schema$GoogleCloudRunV2Container[];
        /**
         * Output only. Represents time when the task was created by the system. It is not guaranteed to be set in happens-before order across separate operations.
         */
        createTime?: string | null;
        /**
         * Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.
         */
        deleteTime?: string | null;
        /**
         * Output only. A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek
         */
        encryptionKey?: string | null;
        /**
         * Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string | null;
        /**
         * Output only. The name of the parent Execution.
         */
        execution?: string | null;
        /**
         * The execution environment being used to host this Task.
         */
        executionEnvironment?: string | null;
        /**
         * Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.
         */
        expireTime?: string | null;
        /**
         * Output only. A number that monotonically increases every time the user modifies the desired state.
         */
        generation?: string | null;
        /**
         * Output only. Index of the Task, unique per execution, and beginning at 0.
         */
        index?: number | null;
        /**
         * Output only. The name of the parent Job.
         */
        job?: string | null;
        /**
         * Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Result of the last attempt of this Task.
         */
        lastAttemptResult?: Schema$GoogleCloudRunV2TaskAttemptResult;
        /**
         * Output only. URI where logs for this execution can be found in Cloud Console.
         */
        logUri?: string | null;
        /**
         * Number of retries allowed per Task, before marking this Task failed.
         */
        maxRetries?: number | null;
        /**
         * Output only. The unique name of this Task.
         */
        name?: string | null;
        /**
         * Output only. The generation of this Task. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.
         */
        observedGeneration?: string | null;
        /**
         * Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The number of times this Task was retried. Tasks are retried when they fail up to the maxRetries limit.
         */
        retried?: number | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. Represents time when the task was scheduled to run by the system. It is not guaranteed to be set in happens-before order across separate operations.
         */
        scheduledTime?: string | null;
        /**
         * Email address of the IAM service account associated with the Task of a Job. The service account represents the identity of the running task, and determines what permissions the task has. If not provided, the task will use the project's default service account.
         */
        serviceAccount?: string | null;
        /**
         * Output only. Represents time when the task started to run. It is not guaranteed to be set in happens-before order across separate operations.
         */
        startTime?: string | null;
        /**
         * Max allowed time duration the Task may be active before the system will actively try to mark it failed and kill associated containers. This applies per attempt of a task, meaning each retry can run for the full timeout.
         */
        timeout?: string | null;
        /**
         * Output only. Server assigned unique identifier for the Task. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.
         */
        uid?: string | null;
        /**
         * Output only. The last-modified time.
         */
        updateTime?: string | null;
        /**
         * A list of Volumes to make available to containers.
         */
        volumes?: Schema$GoogleCloudRunV2Volume[];
        /**
         * Output only. VPC Access configuration to use for this Task. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
         */
        vpcAccess?: Schema$GoogleCloudRunV2VpcAccess;
    }
    /**
     * Result of a task attempt.
     */
    export interface Schema$GoogleCloudRunV2TaskAttemptResult {
        /**
         * Output only. The exit code of this attempt. This may be unset if the container was unable to exit cleanly with a code due to some other failure. See status field for possible failure details.
         */
        exitCode?: number | null;
        /**
         * Output only. The status of this attempt. If the status code is OK, then the attempt succeeded.
         */
        status?: Schema$GoogleRpcStatus;
    }
    /**
     * TaskTemplate describes the data a task should have when created from a template.
     */
    export interface Schema$GoogleCloudRunV2TaskTemplate {
        /**
         * Holds the single container that defines the unit of execution for this task.
         */
        containers?: Schema$GoogleCloudRunV2Container[];
        /**
         * A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek
         */
        encryptionKey?: string | null;
        /**
         * The execution environment being used to host this Task.
         */
        executionEnvironment?: string | null;
        /**
         * Number of retries allowed per Task, before marking this Task failed. Defaults to 3.
         */
        maxRetries?: number | null;
        /**
         * Email address of the IAM service account associated with the Task of a Job. The service account represents the identity of the running task, and determines what permissions the task has. If not provided, the task will use the project's default service account.
         */
        serviceAccount?: string | null;
        /**
         * Max allowed time duration the Task may be active before the system will actively try to mark it failed and kill associated containers. This applies per attempt of a task, meaning each retry can run for the full timeout. Defaults to 600 seconds.
         */
        timeout?: string | null;
        /**
         * A list of Volumes to make available to containers.
         */
        volumes?: Schema$GoogleCloudRunV2Volume[];
        /**
         * VPC Access configuration to use for this Task. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
         */
        vpcAccess?: Schema$GoogleCloudRunV2VpcAccess;
    }
    /**
     * TCPSocketAction describes an action based on opening a socket
     */
    export interface Schema$GoogleCloudRunV2TCPSocketAction {
        /**
         * Port number to access on the container. Must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.
         */
        port?: number | null;
    }
    /**
     * Holds a single traffic routing entry for the Service. Allocations can be done to a specific Revision name, or pointing to the latest Ready Revision.
     */
    export interface Schema$GoogleCloudRunV2TrafficTarget {
        /**
         * Specifies percent of the traffic to this Revision. This defaults to zero if unspecified.
         */
        percent?: number | null;
        /**
         * Revision to which to send this portion of traffic, if traffic allocation is by revision.
         */
        revision?: string | null;
        /**
         * Indicates a string to be part of the URI to exclusively reference this target.
         */
        tag?: string | null;
        /**
         * The allocation type for this traffic target.
         */
        type?: string | null;
    }
    /**
     * Represents the observed state of a single `TrafficTarget` entry.
     */
    export interface Schema$GoogleCloudRunV2TrafficTargetStatus {
        /**
         * Specifies percent of the traffic to this Revision.
         */
        percent?: number | null;
        /**
         * Revision to which this traffic is sent.
         */
        revision?: string | null;
        /**
         * Indicates the string used in the URI to exclusively reference this target.
         */
        tag?: string | null;
        /**
         * The allocation type for this traffic target.
         */
        type?: string | null;
        /**
         * Displays the target URI.
         */
        uri?: string | null;
    }
    /**
     * VersionToPath maps a specific version of a secret to a relative file to mount to, relative to VolumeMount's mount_path.
     */
    export interface Schema$GoogleCloudRunV2VersionToPath {
        /**
         * Integer octal mode bits to use on this file, must be a value between 01 and 0777 (octal). If 0 or not set, the Volume's default mode will be used. Notes * Internally, a umask of 0222 will be applied to any non-zero value. * This is an integer representation of the mode bits. So, the octal integer value should look exactly as the chmod numeric notation with a leading zero. Some examples: for chmod 777 (a=rwx), set to 0777 (octal) or 511 (base-10). For chmod 640 (u=rw,g=r), set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or 493 (base-10). * This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.
         */
        mode?: number | null;
        /**
         * Required. The relative path of the secret in the container.
         */
        path?: string | null;
        /**
         * The Cloud Secret Manager secret version. Can be 'latest' for the latest value, or an integer or a secret alias for a specific version.
         */
        version?: string | null;
    }
    /**
     * Volume represents a named volume in a container.
     */
    export interface Schema$GoogleCloudRunV2Volume {
        /**
         * For Cloud SQL volumes, contains the specific instances that should be mounted. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run.
         */
        cloudSqlInstance?: Schema$GoogleCloudRunV2CloudSqlInstance;
        /**
         * Ephemeral storage used as a shared volume.
         */
        emptyDir?: Schema$GoogleCloudRunV2EmptyDirVolumeSource;
        /**
         * Required. Volume's name.
         */
        name?: string | null;
        /**
         * Secret represents a secret that should populate this volume.
         */
        secret?: Schema$GoogleCloudRunV2SecretVolumeSource;
    }
    /**
     * VolumeMount describes a mounting of a Volume within a container.
     */
    export interface Schema$GoogleCloudRunV2VolumeMount {
        /**
         * Required. Path within the container at which the volume should be mounted. Must not contain ':'. For Cloud SQL volumes, it can be left empty, or must otherwise be `/cloudsql`. All instances defined in the Volume will be available as `/cloudsql/[instance]`. For more information on Cloud SQL volumes, visit https://cloud.google.com/sql/docs/mysql/connect-run
         */
        mountPath?: string | null;
        /**
         * Required. This must match the Name of a Volume.
         */
        name?: string | null;
    }
    /**
     * VPC Access settings. For more information on sending traffic to a VPC network, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
     */
    export interface Schema$GoogleCloudRunV2VpcAccess {
        /**
         * VPC Access connector name. Format: projects/{project\}/locations/{location\}/connectors/{connector\}, where {project\} can be project id or number. For more information on sending traffic to a VPC network via a connector, visit https://cloud.google.com/run/docs/configuring/vpc-connectors.
         */
        connector?: string | null;
        /**
         * Traffic VPC egress settings. If not provided, it defaults to PRIVATE_RANGES_ONLY.
         */
        egress?: string | null;
        /**
         * Direct VPC egress settings. Currently only single network interface is supported.
         */
        networkInterfaces?: Schema$GoogleCloudRunV2NetworkInterface[];
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$GoogleIamV1AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$GoogleIamV1AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$GoogleIamV1AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$GoogleIamV1Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$GoogleTypeExpr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$GoogleIamV1Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$GoogleIamV1AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$GoogleIamV1Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$GoogleIamV1SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$GoogleIamV1Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$GoogleLongrunningListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$GoogleLongrunningOperation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunningOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$GoogleRpcStatus;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The request message for Operations.WaitOperation.
     */
    export interface Schema$GoogleLongrunningWaitOperationRequest {
        /**
         * The maximum duration to wait before timing out. If left blank, the wait will be at most the time permitted by the underlying HTTP/RPC protocol. If RPC context deadline is also specified, the shorter one will be used.
         */
        timeout?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$GoogleProtobufEmpty {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$GoogleRpcStatus {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$GoogleTypeExpr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        jobs: Resource$Projects$Locations$Jobs;
        operations: Resource$Projects$Locations$Operations;
        services: Resource$Projects$Locations$Services;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Jobs {
        context: APIRequestContext;
        executions: Resource$Projects$Locations$Jobs$Executions;
        constructor(context: APIRequestContext);
        /**
         * Creates a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Jobs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Jobs$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        create(params: Params$Resource$Projects$Locations$Jobs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Jobs$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Jobs$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Jobs$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Jobs$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        delete(params: Params$Resource$Projects$Locations$Jobs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Jobs$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Jobs$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets information about a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Jobs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Jobs$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2Job>;
        get(params: Params$Resource$Projects$Locations$Jobs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2Job>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Job>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Get, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Job>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudRunV2Job>): void;
        /**
         * Gets the IAM Access Control policy currently in effect for the given Job. This result does not include any inherited policies.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Jobs$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists Jobs.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Jobs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Jobs$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2ListJobsResponse>;
        list(params: Params$Resource$Projects$Locations$Jobs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2ListJobsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListJobsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$List, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListJobsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListJobsResponse>): void;
        /**
         * Updates a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Jobs$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Jobs$Patch, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        patch(params: Params$Resource$Projects$Locations$Jobs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Jobs$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Jobs$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Triggers creation of a new Execution of this Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        run(params: Params$Resource$Projects$Locations$Jobs$Run, options: StreamMethodOptions): GaxiosPromise<Readable>;
        run(params?: Params$Resource$Projects$Locations$Jobs$Run, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        run(params: Params$Resource$Projects$Locations$Jobs$Run, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        run(params: Params$Resource$Projects$Locations$Jobs$Run, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        run(params: Params$Resource$Projects$Locations$Jobs$Run, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        run(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Sets the IAM Access control policy for the specified Job. Overwrites any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Jobs$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Jobs$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Jobs$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Jobs$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Jobs$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Jobs$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Jobs$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Create extends StandardParameters {
        /**
         * Required. The unique identifier for the Job. The name of the job becomes {parent\}/jobs/{job_id\}.
         */
        jobId?: string;
        /**
         * Required. The location and project in which this Job should be created. Format: projects/{project\}/locations/{location\}, where {project\} can be project id or number.
         */
        parent?: string;
        /**
         * Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2Job;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Delete extends StandardParameters {
        /**
         * A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string;
        /**
         * Required. The full name of the Job. Format: projects/{project\}/locations/{location\}/jobs/{job\}, where {project\} can be project id or number.
         */
        name?: string;
        /**
         * Indicates that the request should be validated without actually deleting any resources.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Get extends StandardParameters {
        /**
         * Required. The full name of the Job. Format: projects/{project\}/locations/{location\}/jobs/{job\}, where {project\} can be project id or number.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Jobs$List extends StandardParameters {
        /**
         * Maximum number of Jobs to return in this call.
         */
        pageSize?: number;
        /**
         * A page token received from a previous call to ListJobs. All other parameters must match.
         */
        pageToken?: string;
        /**
         * Required. The location and project to list resources on. Format: projects/{project\}/locations/{location\}, where {project\} can be project id or number.
         */
        parent?: string;
        /**
         * If true, returns deleted (but unexpired) resources along with active ones.
         */
        showDeleted?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Patch extends StandardParameters {
        /**
         * If set to true, and if the Job does not exist, it will create a new one. Caller must have both create and update permissions for this call if this is set to true.
         */
        allowMissing?: boolean;
        /**
         * The fully qualified name of this Job. Format: projects/{project\}/locations/{location\}/jobs/{job\}
         */
        name?: string;
        /**
         * Indicates that the request should be validated and default values populated, without persisting the request or updating any resources.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2Job;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Run extends StandardParameters {
        /**
         * Required. The full name of the Job. Format: projects/{project\}/locations/{location\}/jobs/{job\}, where {project\} can be project id or number.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2RunJobRequest;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Jobs$Executions {
        context: APIRequestContext;
        tasks: Resource$Projects$Locations$Jobs$Executions$Tasks;
        constructor(context: APIRequestContext);
        /**
         * Cancels an Execution.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Jobs$Executions$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Jobs$Executions$Cancel, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        cancel(params: Params$Resource$Projects$Locations$Jobs$Executions$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Jobs$Executions$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        cancel(params: Params$Resource$Projects$Locations$Jobs$Executions$Cancel, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes an Execution.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Jobs$Executions$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Jobs$Executions$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        delete(params: Params$Resource$Projects$Locations$Jobs$Executions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Jobs$Executions$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Jobs$Executions$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets information about an Execution.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Jobs$Executions$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2Execution>;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2Execution>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Execution>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Get, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Execution>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudRunV2Execution>): void;
        /**
         * Lists Executions from a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Jobs$Executions$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2ListExecutionsResponse>;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2ListExecutionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListExecutionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$List, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListExecutionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListExecutionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$Cancel extends StandardParameters {
        /**
         * Required. The name of the Execution to cancel. Format: `projects/{project\}/locations/{location\}/jobs/{job\}/executions/{execution\}`, where `{project\}` can be project id or number.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2CancelExecutionRequest;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$Delete extends StandardParameters {
        /**
         * A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.
         */
        etag?: string;
        /**
         * Required. The name of the Execution to delete. Format: `projects/{project\}/locations/{location\}/jobs/{job\}/executions/{execution\}`, where `{project\}` can be project id or number.
         */
        name?: string;
        /**
         * Indicates that the request should be validated without actually deleting any resources.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$Get extends StandardParameters {
        /**
         * Required. The full name of the Execution. Format: `projects/{project\}/locations/{location\}/jobs/{job\}/executions/{execution\}`, where `{project\}` can be project id or number.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$List extends StandardParameters {
        /**
         * Maximum number of Executions to return in this call.
         */
        pageSize?: number;
        /**
         * A page token received from a previous call to ListExecutions. All other parameters must match.
         */
        pageToken?: string;
        /**
         * Required. The Execution from which the Executions should be listed. To list all Executions across Jobs, use "-" instead of Job name. Format: `projects/{project\}/locations/{location\}/jobs/{job\}`, where `{project\}` can be project id or number.
         */
        parent?: string;
        /**
         * If true, returns deleted (but unexpired) resources along with active ones.
         */
        showDeleted?: boolean;
    }
    export class Resource$Projects$Locations$Jobs$Executions$Tasks {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a Task.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2Task>;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2Task>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Task>): void;
        get(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Task>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudRunV2Task>): void;
        /**
         * Lists Tasks from an Execution of a Job.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2ListTasksResponse>;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2ListTasksResponse>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListTasksResponse>): void;
        list(params: Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListTasksResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListTasksResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$Tasks$Get extends StandardParameters {
        /**
         * Required. The full name of the Task. Format: projects/{project\}/locations/{location\}/jobs/{job\}/executions/{execution\}/tasks/{task\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Jobs$Executions$Tasks$List extends StandardParameters {
        /**
         * Maximum number of Tasks to return in this call.
         */
        pageSize?: number;
        /**
         * A page token received from a previous call to ListTasks. All other parameters must match.
         */
        pageToken?: string;
        /**
         * Required. The Execution from which the Tasks should be listed. To list all Tasks across Executions of a Job, use "-" instead of Execution name. To list all Tasks across Jobs, use "-" instead of Job name. Format: projects/{project\}/locations/{location\}/jobs/{job\}/executions/{execution\}
         */
        parent?: string;
        /**
         * If true, returns deleted (but unexpired) resources along with active ones.
         */
        showDeleted?: boolean;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleProtobufEmpty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        /**
         * Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: StreamMethodOptions): GaxiosPromise<Readable>;
        wait(params?: Params$Resource$Projects$Locations$Operations$Wait, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        wait(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * Optional. A filter for matching the completed or in-progress operations. The supported formats of *filter* are: To query for only completed operations: done:true To query for only ongoing operations: done:false Must be empty to query for all of the latest operations for the given parent project.
         */
        filter?: string;
        /**
         * Required. To query for all of the operations for a project.
         */
        name?: string;
        /**
         * The maximum number of records that should be returned. Requested page size cannot exceed 100. If not set or set to less than or equal to 0, the default page size is 100. .
         */
        pageSize?: number;
        /**
         * Token identifying which result to start with, which is returned by a previous list call.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Wait extends StandardParameters {
        /**
         * The name of the operation resource to wait on.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleLongrunningWaitOperationRequest;
    }
    export class Resource$Projects$Locations$Services {
        context: APIRequestContext;
        revisions: Resource$Projects$Locations$Services$Revisions;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Service in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Services$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Services$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        create(params: Params$Resource$Projects$Locations$Services$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Services$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Services$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a Service. This will cause the Service to stop serving traffic and will delete all revisions.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Services$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Services$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        delete(params: Params$Resource$Projects$Locations$Services$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Services$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Services$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets information about a Service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Services$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Services$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2Service>;
        get(params: Params$Resource$Projects$Locations$Services$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Services$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2Service>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Service>): void;
        get(params: Params$Resource$Projects$Locations$Services$Get, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Service>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudRunV2Service>): void;
        /**
         * Gets the IAM Access Control policy currently in effect for the given Cloud Run Service. This result does not include any inherited policies.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Services$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Services$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Services$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Services$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Services$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists Services.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Services$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Services$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2ListServicesResponse>;
        list(params: Params$Resource$Projects$Locations$Services$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Services$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2ListServicesResponse>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListServicesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Services$List, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListServicesResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListServicesResponse>): void;
        /**
         * Updates a Service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Services$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Services$Patch, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        patch(params: Params$Resource$Projects$Locations$Services$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Services$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Services$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Sets the IAM Access control policy for the specified Service. Overwrites any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Services$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Services$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Services$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Services$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Services$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Services$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Services$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$GoogleIamV1TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Services$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Services$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Services$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Services$Create extends StandardParameters {
        /**
         * Required. The location and project in which this service should be created. Format: projects/{project\}/locations/{location\}, where {project\} can be project id or number. Only lowercase characters, digits, and hyphens.
         */
        parent?: string;
        /**
         * Required. The unique identifier for the Service. It must begin with letter, and cannot end with hyphen; must contain fewer than 50 characters. The name of the service becomes {parent\}/services/{service_id\}.
         */
        serviceId?: string;
        /**
         * Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2Service;
    }
    export interface Params$Resource$Projects$Locations$Services$Delete extends StandardParameters {
        /**
         * A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.
         */
        etag?: string;
        /**
         * Required. The full name of the Service. Format: projects/{project\}/locations/{location\}/services/{service\}, where {project\} can be project id or number.
         */
        name?: string;
        /**
         * Indicates that the request should be validated without actually deleting any resources.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Services$Get extends StandardParameters {
        /**
         * Required. The full name of the Service. Format: projects/{project\}/locations/{location\}/services/{service\}, where {project\} can be project id or number.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Services$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Services$List extends StandardParameters {
        /**
         * Maximum number of Services to return in this call.
         */
        pageSize?: number;
        /**
         * A page token received from a previous call to ListServices. All other parameters must match.
         */
        pageToken?: string;
        /**
         * Required. The location and project to list resources on. Location must be a valid Google Cloud region, and cannot be the "-" wildcard. Format: projects/{project\}/locations/{location\}, where {project\} can be project id or number.
         */
        parent?: string;
        /**
         * If true, returns deleted (but unexpired) resources along with active ones.
         */
        showDeleted?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Services$Patch extends StandardParameters {
        /**
         * If set to true, and if the Service does not exist, it will create a new one. The caller must have 'run.services.create' permissions if this is set to true and the Service does not exist.
         */
        allowMissing?: boolean;
        /**
         * The fully qualified name of this Service. In CreateServiceRequest, this field is ignored, and instead composed from CreateServiceRequest.parent and CreateServiceRequest.service_id. Format: projects/{project\}/locations/{location\}/services/{service_id\}
         */
        name?: string;
        /**
         * Indicates that the request should be validated and default values populated, without persisting the request or updating any resources.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudRunV2Service;
    }
    export interface Params$Resource$Projects$Locations$Services$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Services$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Services$Revisions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Deletes a Revision.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Services$Revisions$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Services$Revisions$Delete, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        delete(params: Params$Resource$Projects$Locations$Services$Revisions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Services$Revisions$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Services$Revisions$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets information about a Revision.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Services$Revisions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Services$Revisions$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2Revision>;
        get(params: Params$Resource$Projects$Locations$Services$Revisions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Services$Revisions$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2Revision>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Revision>): void;
        get(params: Params$Resource$Projects$Locations$Services$Revisions$Get, callback: BodyResponseCallback<Schema$GoogleCloudRunV2Revision>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudRunV2Revision>): void;
        /**
         * Lists Revisions from a given Service, or from a given location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Services$Revisions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Services$Revisions$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudRunV2ListRevisionsResponse>;
        list(params: Params$Resource$Projects$Locations$Services$Revisions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Services$Revisions$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudRunV2ListRevisionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListRevisionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Services$Revisions$List, callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListRevisionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudRunV2ListRevisionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Services$Revisions$Delete extends StandardParameters {
        /**
         * A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.
         */
        etag?: string;
        /**
         * Required. The name of the Revision to delete. Format: projects/{project\}/locations/{location\}/services/{service\}/revisions/{revision\}
         */
        name?: string;
        /**
         * Indicates that the request should be validated without actually deleting any resources.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Services$Revisions$Get extends StandardParameters {
        /**
         * Required. The full name of the Revision. Format: projects/{project\}/locations/{location\}/services/{service\}/revisions/{revision\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Services$Revisions$List extends StandardParameters {
        /**
         * Maximum number of revisions to return in this call.
         */
        pageSize?: number;
        /**
         * A page token received from a previous call to ListRevisions. All other parameters must match.
         */
        pageToken?: string;
        /**
         * Required. The Service from which the Revisions should be listed. To list all Revisions across Services, use "-" instead of Service name. Format: projects/{project\}/locations/{location\}/services/{service\}
         */
        parent?: string;
        /**
         * If true, returns deleted (but unexpired) resources along with active ones.
         */
        showDeleted?: boolean;
    }
    export {};
}
