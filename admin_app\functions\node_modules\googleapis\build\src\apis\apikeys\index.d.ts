/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { apikeys_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof apikeys_v2.Apikeys;
};
export declare function apikeys(version: 'v2'): apikeys_v2.Apikeys;
export declare function apikeys(options: apikeys_v2.Options): apikeys_v2.Apikeys;
declare const auth: AuthPlus;
export { auth };
export { apikeys_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
