/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { classroom_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof classroom_v1.Classroom;
};
export declare function classroom(version: 'v1'): classroom_v1.Classroom;
export declare function classroom(options: classroom_v1.Options): classroom_v1.Classroom;
declare const auth: AuthPlus;
export { auth };
export { classroom_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
