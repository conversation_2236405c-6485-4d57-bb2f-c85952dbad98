{"name": "googlea<PERSON>", "version": "128.0.0", "repository": "googleapis/google-api-nodejs-client", "license": "Apache-2.0", "description": "Google APIs Client Library for Node.js", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "sideEffects": "false", "engines": {"node": ">=14.0.0"}, "files": ["build/src", "!build/src/**/*.map"], "scripts": {"pretest": "npm run build-test", "prepare": "npm run compile", "test": "c8 mocha build/test", "predocs": "npm run build-tools", "docs": "node build/src/generator/docs", "predocs2": "npm run compile", "docs-extract": "node --max-old-space-size=8192 ./node_modules/@microsoft/api-extractor/bin/api-extractor run --local --verbose", "docs-md": "node --max-old-space-size=8192 ./node_modules/@microsoft/api-documenter/bin/api-documenter markdown --input-folder build/docs --output-folder docs", "docs2": "npm run docs-extract && npm run docs-md", "presystem-test": "npm run build-test", "system-test": "mocha build/system-test", "samples-test": "cd samples && npm install && npm link ../ && pwd && npm test", "lint": "gts check", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc -p tsconfig.json", "prebuild-test": "<PERSON><PERSON><PERSON> build", "build-test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc -p tsconfig.test.json", "build-tools": "tsc -p tsconfig.tools.json", "clean": "gts clean", "fix": "gts fix", "pregenerate": "npm run build-tools", "generate": "node build/src/generator/generator.js", "docs-test": "echo 🙈 this was taking too long and timing out CI", "presubmit-prs": "npm run compile", "submit-prs": "node --max-old-space-size=8192 build/src/generator/synth.js", "prelint": "cd samples; npm link ../; npm i", "predownload": "npm run build-tools", "download": "node build/src/generator/download.js", "preupdate-disclaimers": "npm run build-tools", "update-disclaimers": "node build/src/generator/disclaimer.js"}, "author": "Google Inc.", "keywords": ["google", "api", "google apis", "client", "client library"], "dependencies": {"google-auth-library": "^9.0.0", "googleapis-common": "^7.0.0"}, "devDependencies": {"@compodoc/compodoc": "^1.1.10", "@types/execa": "^0.9.0", "@types/mocha": "^9.0.0", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^20.4.5", "@types/nunjucks": "^3.1.1", "@types/prettier": "2.7.3", "@types/proxyquire": "^1.3.28", "@types/qs": "^6.5.3", "@types/sinon": "^10.0.0", "@types/tmp": "^0.2.0", "@types/url-template": "^2.0.28", "@types/yargs-parser": "^20.0.0", "c8": "^8.0.0", "codecov": "^3.4.0", "cross-env": "^7.0.3", "execa": "^5.0.0", "gaxios": "^6.0.3", "gts": "^5.0.0", "js-green-licenses": "^4.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^13.0.0", "nunjucks": "^3.2.1", "open": "^8.0.0", "p-queue": "^6.0.0", "prettier": "^3.0.0", "proxyquire": "^2.1.3", "rimraf": "^3.0.2", "server-destroy": "^1.0.1", "sinon": "^16.0.0", "tmp": "^0.2.0", "typescript": "5.1.6", "yargs-parser": "^20.2.0"}}