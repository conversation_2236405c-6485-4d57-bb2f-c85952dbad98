/*
 * Copyright 2022 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import { Status } from './constants';

const INAPPROPRIATE_CONTROL_PLANE_CODES: Status[] = [
  Status.OK,
  Status.INVALID_ARGUMENT,
  Status.NOT_FOUND,
  Status.ALREADY_EXISTS,
  Status.FAILED_PRECONDITION,
  Status.ABORTED,
  Status.OUT_OF_RANGE,
  Status.DATA_LOSS,
];

export function restrictControlPlaneStatusCode(
  code: Status,
  details: string
): { code: Status; details: string } {
  if (INAPPROPRIATE_CONTROL_PLANE_CODES.includes(code)) {
    return {
      code: Status.INTERNAL,
      details: `Invalid status from control plane: ${code} ${Status[code]} ${details}`,
    };
  } else {
    return { code, details };
  }
}
