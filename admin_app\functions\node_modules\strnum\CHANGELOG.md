
**1.1.2 / 2025-02-27**
- fix skiplike for 0

**1.1.1 / 2025-02-21**
- All recent fixes of version 2

**2.0.4 / 2025-02-20**
- remove console log

**2.0.3 / 2025-02-20**
- fix for string which are falsly identified as e-notation

**2.0.1 / 2025-02-20**
- fix: handle only zeros
- fix: return original string when NaN

**2.0.0 / 2025-02-20**
- Migrating to ESM modules. No functional change

**1.1.0 / 2025-02-20**
- fix (#9): support missing floating point and e notations