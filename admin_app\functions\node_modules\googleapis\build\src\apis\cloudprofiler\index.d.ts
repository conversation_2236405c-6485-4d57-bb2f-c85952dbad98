/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { cloudprofiler_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof cloudprofiler_v2.Cloudprofiler;
};
export declare function cloudprofiler(version: 'v2'): cloudprofiler_v2.Cloudprofiler;
export declare function cloudprofiler(options: cloudprofiler_v2.Options): cloudprofiler_v2.Cloudprofiler;
declare const auth: AuthPlus;
export { auth };
export { cloudprofiler_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
