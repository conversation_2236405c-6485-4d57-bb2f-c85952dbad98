/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { doubleclickbidmanager_v1_1 } from './v1.1';
import { doubleclickbidmanager_v1 } from './v1';
import { doubleclickbidmanager_v2 } from './v2';
export declare const VERSIONS: {
    'v1.1': typeof doubleclickbidmanager_v1_1.Doubleclickbidmanager;
    v1: typeof doubleclickbidmanager_v1.Doubleclickbidmanager;
    v2: typeof doubleclickbidmanager_v2.Doubleclickbidmanager;
};
export declare function doubleclickbidmanager(version: 'v1.1'): doubleclickbidmanager_v1_1.Doubleclickbidmanager;
export declare function doubleclickbidmanager(options: doubleclickbidmanager_v1_1.Options): doubleclickbidmanager_v1_1.Doubleclickbidmanager;
export declare function doubleclickbidmanager(version: 'v1'): doubleclickbidmanager_v1.Doubleclickbidmanager;
export declare function doubleclickbidmanager(options: doubleclickbidmanager_v1.Options): doubleclickbidmanager_v1.Doubleclickbidmanager;
export declare function doubleclickbidmanager(version: 'v2'): doubleclickbidmanager_v2.Doubleclickbidmanager;
export declare function doubleclickbidmanager(options: doubleclickbidmanager_v2.Options): doubleclickbidmanager_v2.Doubleclickbidmanager;
declare const auth: AuthPlus;
export { auth };
export { doubleclickbidmanager_v1_1 };
export { doubleclickbidmanager_v1 };
export { doubleclickbidmanager_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
