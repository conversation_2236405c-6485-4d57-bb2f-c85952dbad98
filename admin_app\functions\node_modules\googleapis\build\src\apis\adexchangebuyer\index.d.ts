/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { adexchangebuyer_v1_2 } from './v1.2';
import { adexchangebuyer_v1_3 } from './v1.3';
import { adexchangebuyer_v1_4 } from './v1.4';
export declare const VERSIONS: {
    'v1.2': typeof adexchangebuyer_v1_2.Adexchangebuyer;
    'v1.3': typeof adexchangebuyer_v1_3.Adexchangebuyer;
    'v1.4': typeof adexchangebuyer_v1_4.Adexchangebuyer;
};
export declare function adexchangebuyer(version: 'v1.2'): adexchangebuyer_v1_2.Adexchangebuyer;
export declare function adexchangebuyer(options: adexchangebuyer_v1_2.Options): adexchangebuyer_v1_2.Adexchangebuyer;
export declare function adexchangebuyer(version: 'v1.3'): adexchangebuyer_v1_3.Adexchangebuyer;
export declare function adexchangebuyer(options: adexchangebuyer_v1_3.Options): adexchangebuyer_v1_3.Adexchangebuyer;
export declare function adexchangebuyer(version: 'v1.4'): adexchangebuyer_v1_4.Adexchangebuyer;
export declare function adexchangebuyer(options: adexchangebuyer_v1_4.Options): adexchangebuyer_v1_4.Adexchangebuyer;
declare const auth: AuthPlus;
export { auth };
export { adexchangebuyer_v1_2 };
export { adexchangebuyer_v1_3 };
export { adexchangebuyer_v1_4 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
