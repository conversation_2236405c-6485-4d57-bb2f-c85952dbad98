/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { playablelocations_v3 } from './v3';
export declare const VERSIONS: {
    v3: typeof playablelocations_v3.Playablelocations;
};
export declare function playablelocations(version: 'v3'): playablelocations_v3.Playablelocations;
export declare function playablelocations(options: playablelocations_v3.Options): playablelocations_v3.Playablelocations;
declare const auth: AuthPlus;
export { auth };
export { playablelocations_v3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
