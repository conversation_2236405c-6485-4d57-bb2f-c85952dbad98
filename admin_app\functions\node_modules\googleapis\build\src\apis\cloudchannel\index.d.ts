/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { cloudchannel_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof cloudchannel_v1.Cloudchannel;
};
export declare function cloudchannel(version: 'v1'): cloudchannel_v1.Cloudchannel;
export declare function cloudchannel(options: cloudchannel_v1.Options): cloudchannel_v1.Cloudchannel;
declare const auth: AuthPlus;
export { auth };
export { cloudchannel_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
