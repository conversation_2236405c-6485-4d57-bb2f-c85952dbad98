/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { batch_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof batch_v1.Batch;
};
export declare function batch(version: 'v1'): batch_v1.Batch;
export declare function batch(options: batch_v1.Options): batch_v1.Batch;
declare const auth: AuthPlus;
export { auth };
export { batch_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
