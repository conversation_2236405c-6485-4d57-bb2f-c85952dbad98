/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { gamesConfiguration_v1configuration } from './v1configuration';
export declare const VERSIONS: {
    v1configuration: typeof gamesConfiguration_v1configuration.Gamesconfiguration;
};
export declare function gamesConfiguration(version: 'v1configuration'): gamesConfiguration_v1configuration.Gamesconfiguration;
export declare function gamesConfiguration(options: gamesConfiguration_v1configuration.Options): gamesConfiguration_v1configuration.Gamesconfiguration;
declare const auth: AuthPlus;
export { auth };
export { gamesConfiguration_v1configuration };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
