{"version": 3, "file": "locationService.js", "sourceRoot": "", "sources": ["../../src/locationService.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAEjC,mBAAmB;AACnB,6BAA6B;AAC7B,yCAAgC;AAGhC,mDAA8C;AAE9C,iDAAiD;AAQjD,qEAAgE;AAChE,uDAAwD;AAIxD;;GAEG;AACH,8DAA8D;AAE9D,MAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;AAEtD;;;GAGG;AACH,MAAa,eAAe;IAoB1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,YACE,OAAwC;IACxC,8DAA8D;IAC9D,IAAmB;;QAxDb,gBAAW,GAAG,KAAK,CAAC;QAM5B,gBAAW,GAAgB;YACzB,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;SACb,CAAC;QA+CA,uDAAuD;QACvD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,CAAC,WAAqC,CAAC;QACjE,MAAM,WAAW,GACf,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,MAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAA,IAAI,aAAa,CAAC,WAAW,CAAC;QACtE,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAClC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,MAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAA,CACvC,CAAC;QACF,MAAM,IAAI,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,aAAa,CAAC,IAAI,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,mCAAI,EAAE,CAAC;QAC9C,MAAM,QAAQ,GACZ,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,mCACd,CAAC,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAA,KAAK,UAAU,CAAC,CAAC;QACzE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAC,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAC,EAAE,IAAI,CAAC,CAAC;QAExE,0GAA0G;QAC1G,IAAI,WAAW,KAAK,aAAa,CAAC,WAAW,IAAI,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QACxC,CAAC;QAED,8CAA8C;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,gEAAgE;QAChE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAkB,CAAC;QAEvC,mDAAmD;QACnD,IAAI,WAAW,KAAK,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;QACjD,CAAC;QAED,sCAAsC;QACtC,MAAM,YAAY,GAAG,CAAC,OAAO,OAAO,EAAE,EAAE,SAAS,OAAO,EAAE,CAAC,CAAC;QAC5D,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;YAC5D,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,8BAA8B;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEjD,8DAA8D;QAC9D,4DAA4D;QAC5D,2DAA2D;QAC3D,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG;YACtB,aAAa,EAAE,IAAI,+BAAc,CAC/B,WAAW,EACX,eAAe,EACf,WAAW,CACZ;SACF,CAAC;QAEF,uDAAuD;QACvD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,iBAAiB,CACxC,iCAAiC,EACjC,WAA+B,EAC/B,IAAI,CAAC,YAAY,IAAI,EAAE,EACvB,EAAC,mBAAmB,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAC9C,CAAC;QAEF,oEAAoE;QACpE,gEAAgE;QAChE,4DAA4D;QAC5D,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAExB,4EAA4E;QAC5E,IAAI,CAAC,IAAI,GAAG,eAAI,CAAC;IACnB,CAAC;IAED;;;;;;;;;;OAUG;IACH,UAAU;QACR,yEAAyE;QACzE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;QAED,sCAAsC;QACtC,mCAAmC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1C,IAAI,CAAC,KAAK,CAAC,QAAQ;YACjB,CAAC,CAAE,IAAI,CAAC,OAAyB,CAAC,aAAa,CAC3C,iCAAiC,CAClC;YACH,CAAC,CAAC,8DAA8D;gBAC7D,IAAI,CAAC,OAAe,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,EACzD,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,0BAA0B,CACS,CAAC;QAE3C,6DAA6D;QAC7D,0CAA0C;QAC1C,MAAM,oBAAoB,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;QAC9D,KAAK,MAAM,UAAU,IAAI,oBAAoB,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CACzC,IAAI,CAAC,EAAE,CACL,CAAC,GAAG,IAAe,EAAE,EAAE;gBACrB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,OAAO,OAAO,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC;gBAC/D,CAAC;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC,EACH,CAAC,GAA6B,EAAE,EAAE,CAAC,GAAG,EAAE;gBACtC,MAAM,GAAG,CAAC;YACZ,CAAC,CACF,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC;YAClE,MAAM,OAAO,GAAG,IAAA,6BAAa,EAC3B,WAAW,EACX,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC1B,UAAU,CACX,CAAC;YAEF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,MAAM,KAAK,WAAW;QACpB,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,MAAM,KAAK,WAAW;QACpB,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,MAAM,KAAK,IAAI;QACb,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,MAAM,KAAK,MAAM;QACf,OAAO,CAAC,gDAAgD,CAAC,CAAC;IAC5D,CAAC;IAQD,YAAY,CACV,QAAiD;QAEjD,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO;QACT,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;IAClC,CAAC;IA0BD;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,CACT,OAAyD,EACzD,iBAMK,EACL,QAIC;QAED,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,OAAwB,CAAC;QAC7B,IAAI,OAAO,iBAAiB,KAAK,UAAU,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACtE,QAAQ,GAAG,iBAAiB,CAAC;YAC7B,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,iBAAoC,CAAC;QACjD,CAAC;QACD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IA6BD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,aAAa,CACX,OAA2D,EAC3D,iBAQK,EACL,QAIC;QAQD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,OAAwB,CAAC;QAC7B,IAAI,OAAO,iBAAiB,KAAK,UAAU,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACtE,QAAQ,GAAG,iBAAiB,CAAC;YAC7B,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,iBAAoC,CAAC;QACjD,CAAC;QACD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACH,kBAAkB,CAChB,OAA2D,EAC3D,OAAyB;QAEzB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CACrD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAY,EAC9C,OAAiC,EACjC,YAAY,CAC4C,CAAC;IAC7D,CAAC;IAED;;;;;OAKG;IACH,KAAK;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF;AAreD,0CAqeC"}