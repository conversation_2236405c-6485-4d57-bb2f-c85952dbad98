/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { area120tables_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof area120tables_v1alpha1.Area120tables;
};
export declare function area120tables(version: 'v1alpha1'): area120tables_v1alpha1.Area120tables;
export declare function area120tables(options: area120tables_v1alpha1.Options): area120tables_v1alpha1.Area120tables;
declare const auth: AuthPlus;
export { auth };
export { area120tables_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
