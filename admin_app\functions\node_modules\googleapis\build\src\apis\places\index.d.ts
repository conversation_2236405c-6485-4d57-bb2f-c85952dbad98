/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { places_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof places_v1.Places;
};
export declare function places(version: 'v1'): places_v1.Places;
export declare function places(options: places_v1.Options): places_v1.Places;
declare const auth: AuthPlus;
export { auth };
export { places_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
