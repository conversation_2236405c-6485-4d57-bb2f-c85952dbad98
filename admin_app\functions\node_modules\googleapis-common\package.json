{"name": "googleapis-common", "version": "7.2.0", "description": "A common tooling library used by the googleapis npm module. You probably don't want to use this directly.", "repository": "googleapis/nodejs-googleapis-common", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src", "!build/src/**/*.map"], "scripts": {"prebenchmark": "npm run compile", "benchmark": "node build/benchmark/bench.js", "compile": "tsc -p .", "test": "c8 mocha build/test", "system-test": "c8 mocha build/system-test --timeout 600000", "presystem-test": "npm run compile", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "lint": "gts check", "samples-test": "mocha build/samples-test", "docs": "compodoc src/", "docs-test": "linkinator docs", "webpack": "webpack", "browser-test": "karma start", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "keywords": [], "author": "Google LLC", "license": "Apache-2.0", "dependencies": {"extend": "^3.0.2", "gaxios": "^6.0.3", "google-auth-library": "^9.7.0", "qs": "^6.7.0", "url-template": "^2.0.8", "uuid": "^9.0.0"}, "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@compodoc/compodoc": "1.1.23", "@types/execa": "^0.9.0", "@types/extend": "^3.0.1", "@types/mocha": "^9.0.0", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/nock": "^11.0.0", "@types/proxyquire": "^1.3.28", "@types/qs": "^6.5.3", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "@types/url-template": "^2.0.28", "@types/uuid": "^9.0.0", "c8": "^8.0.0", "codecov": "^3.5.0", "execa": "^5.0.0", "gts": "^5.0.0", "http2spy": "^2.0.0", "is-docker": "^2.0.0", "karma": "^6.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.0", "karma-remap-coverage": "^0.1.5", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "^4.0.0", "linkinator": "^3.1.0", "mocha": "^9.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^13.0.0", "null-loader": "^4.0.0", "proxyquire": "^2.1.3", "puppeteer": "^18.2.1", "sinon": "^17.0.0", "tmp": "^0.2.0", "ts-loader": "^8.0.0", "typescript": "5.1.6", "webpack": "^4.0.0", "webpack-cli": "^4.0.0"}, "engines": {"node": ">=14.0.0"}}