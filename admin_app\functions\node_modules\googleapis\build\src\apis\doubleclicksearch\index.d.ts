/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { doubleclicksearch_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof doubleclicksearch_v2.Doubleclicksearch;
};
export declare function doubleclicksearch(version: 'v2'): doubleclicksearch_v2.Doubleclicksearch;
export declare function doubleclicksearch(options: doubleclicksearch_v2.Options): doubleclicksearch_v2.Doubleclicksearch;
declare const auth: AuthPlus;
export { auth };
export { doubleclicksearch_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
