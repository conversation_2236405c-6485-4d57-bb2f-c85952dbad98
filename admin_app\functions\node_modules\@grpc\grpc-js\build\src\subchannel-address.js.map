{"version": 3, "file": "subchannel-address.js", "sourceRoot": "", "sources": ["../../src/subchannel-address.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAqBH,wDAIC;AAED,wDAmBC;AAED,8DAUC;AAID,8DAcC;AAMD,sCAYC;AAED,4CAIC;AAED,gDAUC;AA9GD,6BAAmC;AAmBnC,SAAgB,sBAAsB,CACpC,OAA0B;IAE1B,OAAO,MAAM,IAAI,OAAO,CAAC;AAC3B,CAAC;AAED,SAAgB,sBAAsB,CACpC,QAA4B,EAC5B,QAA4B;IAE5B,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACrC,OAAO,CACL,sBAAsB,CAAC,QAAQ,CAAC;YAChC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;YAC/B,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAChC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC;IAC9E,CAAC;AACH,CAAC;AAED,SAAgB,yBAAyB,CAAC,OAA0B;IAClE,IAAI,sBAAsB,CAAC,OAAO,CAAC,EAAE,CAAC;QACpC,IAAI,IAAA,YAAM,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;QAC3C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC,IAAI,CAAC;IACtB,CAAC;AACH,CAAC;AAED,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB,SAAgB,yBAAyB,CACvC,aAAqB,EACrB,IAAa;IAEb,IAAI,IAAA,UAAI,EAAC,aAAa,CAAC,EAAE,CAAC;QACxB,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,YAAY;SAC3B,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO;YACL,IAAI,EAAE,aAAa;SACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAMD,SAAgB,aAAa,CAAC,SAAmB,EAAE,SAAmB;IACpE,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpD,IACE,CAAC,sBAAsB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACvE,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,gBAAgB,CAAC,QAAkB;IACjD,OAAO,CACL,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CACzE,CAAC;AACJ,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAkB,EAClB,eAAkC;IAElC,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;QACzC,IAAI,sBAAsB,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,sBAAsB,CAC7B,SAAmB,EACnB,SAAmB;IAEnB,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3C,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC/C,UAAU,GAAG,IAAI,CAAC;gBAClB,MAAM;YACR,CAAC;QACH,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAa,WAAW;IAAxB;QACU,QAAG,GAAqC,IAAI,GAAG,EAAE,CAAC;IA8F5D,CAAC;IA5FC,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,uBAAuB,CAAC,OAA0B;QAChD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC3C,OAAO,KAAK,CAAC,KAAK,CAAC;YACrB,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,SAAqB;QACjC,MAAM,aAAa,GAAgB,EAAE,CAAC;QACtC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;oBAChD,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;YACD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAChC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,GAAG,CAAC,QAAkB;QACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChD,OAAO,KAAK,CAAC,KAAK,CAAC;YACrB,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,GAAG,CAAC,QAAkB,EAAE,QAAmB;QACzC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACvB,OAAO;YACT,CAAC;QACH,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,QAAkB;QACvB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACvB,OAAO;YACT,CAAC;QACH,CAAC;IACH,CAAC;IAED,GAAG,CAAC,QAAkB;QACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK;QACH,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IAED,CAAC,IAAI;QACH,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,KAAK,CAAC,GAAG,CAAC;QAClB,CAAC;IACH,CAAC;IAED,CAAC,MAAM;QACL,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,KAAK,CAAC,KAAK,CAAC;QACpB,CAAC;IACH,CAAC;IAED,CAAC,OAAO;QACN,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;CACF;AA/FD,kCA+FC"}