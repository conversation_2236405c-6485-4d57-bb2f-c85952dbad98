/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { trafficdirector_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof trafficdirector_v2.Trafficdirector;
};
export declare function trafficdirector(version: 'v2'): trafficdirector_v2.Trafficdirector;
export declare function trafficdirector(options: trafficdirector_v2.Options): trafficdirector_v2.Trafficdirector;
declare const auth: AuthPlus;
export { auth };
export { trafficdirector_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
