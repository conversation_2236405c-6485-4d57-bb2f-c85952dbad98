/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebase_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof firebase_v1beta1.Firebase;
};
export declare function firebase(version: 'v1beta1'): firebase_v1beta1.Firebase;
export declare function firebase(options: firebase_v1beta1.Options): firebase_v1beta1.Firebase;
declare const auth: AuthPlus;
export { auth };
export { firebase_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
