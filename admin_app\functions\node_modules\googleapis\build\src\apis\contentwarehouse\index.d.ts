/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { contentwarehouse_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof contentwarehouse_v1.Contentwarehouse;
};
export declare function contentwarehouse(version: 'v1'): contentwarehouse_v1.Contentwarehouse;
export declare function contentwarehouse(options: contentwarehouse_v1.Options): contentwarehouse_v1.Contentwarehouse;
declare const auth: AuthPlus;
export { auth };
export { contentwarehouse_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
