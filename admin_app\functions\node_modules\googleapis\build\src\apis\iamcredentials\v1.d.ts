/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace iamcredentials_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * IAM Service Account Credentials API
     *
     * Creates short-lived credentials for impersonating IAM service accounts. To enable this API, you must enable the IAM API (iam.googleapis.com).
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const iamcredentials = google.iamcredentials('v1');
     * ```
     */
    export class Iamcredentials {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    export interface Schema$GenerateAccessTokenRequest {
        /**
         * The sequence of service accounts in a delegation chain. This field is required for [delegated requests](https://cloud.google.com/iam/help/credentials/delegated-request). For [direct requests](https://cloud.google.com/iam/help/credentials/direct-request), which are more common, do not specify this field. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        delegates?: string[] | null;
        /**
         * The desired lifetime duration of the access token in seconds. By default, the maximum allowed value is 1 hour. To set a lifetime of up to 12 hours, you can add the service account as an allowed value in an Organization Policy that enforces the `constraints/iam.allowServiceAccountCredentialLifetimeExtension` constraint. See detailed instructions at https://cloud.google.com/iam/help/credentials/lifetime If a value is not specified, the token's lifetime will be set to a default value of 1 hour.
         */
        lifetime?: string | null;
        /**
         * Required. Code to identify the scopes to be included in the OAuth 2.0 access token. See https://developers.google.com/identity/protocols/googlescopes for more information. At least one value required.
         */
        scope?: string[] | null;
    }
    export interface Schema$GenerateAccessTokenResponse {
        /**
         * The OAuth 2.0 access token.
         */
        accessToken?: string | null;
        /**
         * Token expiration time. The expiration time is always set.
         */
        expireTime?: string | null;
    }
    export interface Schema$GenerateIdTokenRequest {
        /**
         * Required. The audience for the token, such as the API or account that this token grants access to.
         */
        audience?: string | null;
        /**
         * The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        delegates?: string[] | null;
        /**
         * Include the service account email in the token. If set to `true`, the token will contain `email` and `email_verified` claims.
         */
        includeEmail?: boolean | null;
    }
    export interface Schema$GenerateIdTokenResponse {
        /**
         * The OpenId Connect ID token.
         */
        token?: string | null;
    }
    export interface Schema$SignBlobRequest {
        /**
         * The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        delegates?: string[] | null;
        /**
         * Required. The bytes to sign.
         */
        payload?: string | null;
    }
    export interface Schema$SignBlobResponse {
        /**
         * The ID of the key used to sign the blob. The key used for signing will remain valid for at least 12 hours after the blob is signed. To verify the signature, you can retrieve the public key in several formats from the following endpoints: - RSA public key wrapped in an X.509 v3 certificate: `https://www.googleapis.com/service_accounts/v1/metadata/x509/{ACCOUNT_EMAIL\}` - Raw key in JSON format: `https://www.googleapis.com/service_accounts/v1/metadata/raw/{ACCOUNT_EMAIL\}` - JSON Web Key (JWK): `https://www.googleapis.com/service_accounts/v1/metadata/jwk/{ACCOUNT_EMAIL\}`
         */
        keyId?: string | null;
        /**
         * The signature for the blob. Does not include the original blob. After the key pair referenced by the `key_id` response field expires, Google no longer exposes the public key that can be used to verify the blob. As a result, the receiver can no longer verify the signature.
         */
        signedBlob?: string | null;
    }
    export interface Schema$SignJwtRequest {
        /**
         * The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        delegates?: string[] | null;
        /**
         * Required. The JWT payload to sign. Must be a serialized JSON object that contains a JWT Claims Set. For example: `{"sub": "<EMAIL>", "iat": 313435\}` If the JWT Claims Set contains an expiration time (`exp`) claim, it must be an integer timestamp that is not in the past and no more than 12 hours in the future.
         */
        payload?: string | null;
    }
    export interface Schema$SignJwtResponse {
        /**
         * The ID of the key used to sign the JWT. The key used for signing will remain valid for at least 12 hours after the JWT is signed. To verify the signature, you can retrieve the public key in several formats from the following endpoints: - RSA public key wrapped in an X.509 v3 certificate: `https://www.googleapis.com/service_accounts/v1/metadata/x509/{ACCOUNT_EMAIL\}` - Raw key in JSON format: `https://www.googleapis.com/service_accounts/v1/metadata/raw/{ACCOUNT_EMAIL\}` - JSON Web Key (JWK): `https://www.googleapis.com/service_accounts/v1/metadata/jwk/{ACCOUNT_EMAIL\}`
         */
        keyId?: string | null;
        /**
         * The signed JWT. Contains the automatically generated header; the client-supplied payload; and the signature, which is generated using the key referenced by the `kid` field in the header. After the key pair referenced by the `key_id` response field expires, Google no longer exposes the public key that can be used to verify the JWT. As a result, the receiver can no longer verify the signature.
         */
        signedJwt?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        serviceAccounts: Resource$Projects$Serviceaccounts;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Serviceaccounts {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Generates an OAuth 2.0 access token for a service account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateAccessToken(params: Params$Resource$Projects$Serviceaccounts$Generateaccesstoken, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generateAccessToken(params?: Params$Resource$Projects$Serviceaccounts$Generateaccesstoken, options?: MethodOptions): GaxiosPromise<Schema$GenerateAccessTokenResponse>;
        generateAccessToken(params: Params$Resource$Projects$Serviceaccounts$Generateaccesstoken, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateAccessToken(params: Params$Resource$Projects$Serviceaccounts$Generateaccesstoken, options: MethodOptions | BodyResponseCallback<Schema$GenerateAccessTokenResponse>, callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        generateAccessToken(params: Params$Resource$Projects$Serviceaccounts$Generateaccesstoken, callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        generateAccessToken(callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        /**
         * Generates an OpenID Connect ID token for a service account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateIdToken(params: Params$Resource$Projects$Serviceaccounts$Generateidtoken, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generateIdToken(params?: Params$Resource$Projects$Serviceaccounts$Generateidtoken, options?: MethodOptions): GaxiosPromise<Schema$GenerateIdTokenResponse>;
        generateIdToken(params: Params$Resource$Projects$Serviceaccounts$Generateidtoken, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateIdToken(params: Params$Resource$Projects$Serviceaccounts$Generateidtoken, options: MethodOptions | BodyResponseCallback<Schema$GenerateIdTokenResponse>, callback: BodyResponseCallback<Schema$GenerateIdTokenResponse>): void;
        generateIdToken(params: Params$Resource$Projects$Serviceaccounts$Generateidtoken, callback: BodyResponseCallback<Schema$GenerateIdTokenResponse>): void;
        generateIdToken(callback: BodyResponseCallback<Schema$GenerateIdTokenResponse>): void;
        /**
         * Signs a blob using a service account's system-managed private key.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        signBlob(params: Params$Resource$Projects$Serviceaccounts$Signblob, options: StreamMethodOptions): GaxiosPromise<Readable>;
        signBlob(params?: Params$Resource$Projects$Serviceaccounts$Signblob, options?: MethodOptions): GaxiosPromise<Schema$SignBlobResponse>;
        signBlob(params: Params$Resource$Projects$Serviceaccounts$Signblob, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        signBlob(params: Params$Resource$Projects$Serviceaccounts$Signblob, options: MethodOptions | BodyResponseCallback<Schema$SignBlobResponse>, callback: BodyResponseCallback<Schema$SignBlobResponse>): void;
        signBlob(params: Params$Resource$Projects$Serviceaccounts$Signblob, callback: BodyResponseCallback<Schema$SignBlobResponse>): void;
        signBlob(callback: BodyResponseCallback<Schema$SignBlobResponse>): void;
        /**
         * Signs a JWT using a service account's system-managed private key.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        signJwt(params: Params$Resource$Projects$Serviceaccounts$Signjwt, options: StreamMethodOptions): GaxiosPromise<Readable>;
        signJwt(params?: Params$Resource$Projects$Serviceaccounts$Signjwt, options?: MethodOptions): GaxiosPromise<Schema$SignJwtResponse>;
        signJwt(params: Params$Resource$Projects$Serviceaccounts$Signjwt, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        signJwt(params: Params$Resource$Projects$Serviceaccounts$Signjwt, options: MethodOptions | BodyResponseCallback<Schema$SignJwtResponse>, callback: BodyResponseCallback<Schema$SignJwtResponse>): void;
        signJwt(params: Params$Resource$Projects$Serviceaccounts$Signjwt, callback: BodyResponseCallback<Schema$SignJwtResponse>): void;
        signJwt(callback: BodyResponseCallback<Schema$SignJwtResponse>): void;
    }
    export interface Params$Resource$Projects$Serviceaccounts$Generateaccesstoken extends StandardParameters {
        /**
         * Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GenerateAccessTokenRequest;
    }
    export interface Params$Resource$Projects$Serviceaccounts$Generateidtoken extends StandardParameters {
        /**
         * Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GenerateIdTokenRequest;
    }
    export interface Params$Resource$Projects$Serviceaccounts$Signblob extends StandardParameters {
        /**
         * Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SignBlobRequest;
    }
    export interface Params$Resource$Projects$Serviceaccounts$Signjwt extends StandardParameters {
        /**
         * Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID\}`. The `-` wildcard character is required; replacing it with a project ID is invalid.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SignJwtRequest;
    }
    export {};
}
