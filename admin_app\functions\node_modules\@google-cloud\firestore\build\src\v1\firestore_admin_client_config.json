{"interfaces": {"google.firestore.admin.v1.FirestoreAdmin": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "deadline_exceeded_internal_unavailable": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateIndex": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListIndexes": {"timeout_millis": 60000, "retry_codes_name": "deadline_exceeded_internal_unavailable", "retry_params_name": "default"}, "GetIndex": {"timeout_millis": 60000, "retry_codes_name": "deadline_exceeded_internal_unavailable", "retry_params_name": "default"}, "DeleteIndex": {"timeout_millis": 60000, "retry_codes_name": "deadline_exceeded_internal_unavailable", "retry_params_name": "default"}, "GetField": {"timeout_millis": 60000, "retry_codes_name": "deadline_exceeded_internal_unavailable", "retry_params_name": "default"}, "UpdateField": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFields": {"timeout_millis": 60000, "retry_codes_name": "deadline_exceeded_internal_unavailable", "retry_params_name": "default"}, "ExportDocuments": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportDocuments": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BulkDeleteDocuments": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateDatabase": {"timeout_millis": 120000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDatabase": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDatabases": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateDatabase": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDatabase": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetBackup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListBackups": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteBackup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "RestoreDatabase": {"timeout_millis": 120000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateBackupSchedule": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetBackupSchedule": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListBackupSchedules": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateBackupSchedule": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteBackupSchedule": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}