/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { gkebackup_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof gkebackup_v1.Gkebackup;
};
export declare function gkebackup(version: 'v1'): gkebackup_v1.Gkebackup;
export declare function gkebackup(options: gkebackup_v1.Options): gkebackup_v1.Gkebackup;
declare const auth: AuthPlus;
export { auth };
export { gkebackup_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
