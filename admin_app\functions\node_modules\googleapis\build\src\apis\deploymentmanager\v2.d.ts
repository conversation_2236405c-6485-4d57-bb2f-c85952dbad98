/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace deploymentmanager_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Deployment Manager V2 API
     *
     * The Google Cloud Deployment Manager v2 API provides services for configuring, deploying, and viewing Google Cloud services and APIs via templates which specify deployments of Cloud resources.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const deploymentmanager = google.deploymentmanager('v2');
     * ```
     */
    export class Deploymentmanager {
        context: APIRequestContext;
        deployments: Resource$Deployments;
        manifests: Resource$Manifests;
        operations: Resource$Operations;
        resources: Resource$Resources;
        types: Resource$Types;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    export interface Schema$BulkInsertOperationStatus {
        /**
         * [Output Only] Count of VMs successfully created so far.
         */
        createdVmCount?: number | null;
        /**
         * [Output Only] Count of VMs that got deleted during rollback.
         */
        deletedVmCount?: number | null;
        /**
         * [Output Only] Count of VMs that started creating but encountered an error.
         */
        failedToCreateVmCount?: number | null;
        /**
         * [Output Only] Creation status of BulkInsert operation - information if the flow is rolling forward or rolling back.
         */
        status?: string | null;
        /**
         * [Output Only] Count of VMs originally planned to be created.
         */
        targetVmCount?: number | null;
    }
    export interface Schema$ConfigFile {
        /**
         * The contents of the file.
         */
        content?: string | null;
    }
    export interface Schema$Deployment {
        /**
         * An optional user-provided description of the deployment.
         */
        description?: string | null;
        /**
         * Provides a fingerprint to use in requests to modify a deployment, such as `update()`, `stop()`, and `cancelPreview()` requests. A fingerprint is a randomly generated value that must be provided with `update()`, `stop()`, and `cancelPreview()` requests to perform optimistic locking. This ensures optimistic concurrency so that only one request happens at a time. The fingerprint is initially generated by Deployment Manager and changes after every request to modify data. To get the latest fingerprint value, perform a `get()` request to a deployment.
         */
        fingerprint?: string | null;
        id?: string | null;
        /**
         * Output only. Creation timestamp in RFC3339 text format.
         */
        insertTime?: string | null;
        /**
         * Map of One Platform labels; provided by the client when the resource is created or updated. Specifically: Label keys must be between 1 and 63 characters long and must conform to the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0 and 63 characters long and must conform to the regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
         */
        labels?: Schema$DeploymentLabelEntry[];
        /**
         * Output only. URL of the manifest representing the last manifest that was successfully deployed. If no manifest has been successfully deployed, this field will be absent.
         */
        manifest?: string | null;
        /**
         * Name of the resource; provided by the client when the resource is created. The name must be 1-63 characters long, and comply with RFC1035. Specifically, the name must be 1-63 characters long and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])?` which means the first character must be a lowercase letter, and all following characters must be a dash, lowercase letter, or digit, except the last character, which cannot be a dash.
         */
        name?: string | null;
        /**
         * Output only. The Operation that most recently ran, or is currently running, on this deployment.
         */
        operation?: Schema$Operation;
        /**
         * Output only. Server defined URL for the resource.
         */
        selfLink?: string | null;
        /**
         * [Input Only] The parameters that define your deployment, including the deployment configuration and relevant templates.
         */
        target?: Schema$TargetConfiguration;
        /**
         * Output only. If Deployment Manager is currently updating or previewing an update to this deployment, the updated configuration appears here.
         */
        update?: Schema$DeploymentUpdate;
        /**
         * Output only. Update timestamp in RFC3339 text format.
         */
        updateTime?: string | null;
    }
    /**
     * Label object for Deployments
     */
    export interface Schema$DeploymentLabelEntry {
        /**
         * Key of the label
         */
        key?: string | null;
        /**
         * Value of the label
         */
        value?: string | null;
    }
    export interface Schema$DeploymentsCancelPreviewRequest {
        /**
         * Specifies a fingerprint for `cancelPreview()` requests. A fingerprint is a randomly generated value that must be provided in `cancelPreview()` requests to perform optimistic locking. This ensures optimistic concurrency so that the deployment does not have conflicting requests (e.g. if someone attempts to make a new update request while another user attempts to cancel a preview, this would prevent one of the requests). The fingerprint is initially generated by Deployment Manager and changes after every request to modify a deployment. To get the latest fingerprint value, perform a `get()` request on the deployment.
         */
        fingerprint?: string | null;
    }
    /**
     * A response containing a partial list of deployments and a page token used to build the next request if the request has been truncated.
     */
    export interface Schema$DeploymentsListResponse {
        /**
         * Output only. The deployments contained in this response.
         */
        deployments?: Schema$Deployment[];
        /**
         * Output only. A token used to continue a truncated list request.
         */
        nextPageToken?: string | null;
    }
    export interface Schema$DeploymentsStopRequest {
        /**
         * Specifies a fingerprint for `stop()` requests. A fingerprint is a randomly generated value that must be provided in `stop()` requests to perform optimistic locking. This ensures optimistic concurrency so that the deployment does not have conflicting requests (e.g. if someone attempts to make a new update request while another user attempts to stop an ongoing update request, this would prevent a collision). The fingerprint is initially generated by Deployment Manager and changes after every request to modify a deployment. To get the latest fingerprint value, perform a `get()` request on the deployment.
         */
        fingerprint?: string | null;
    }
    export interface Schema$DeploymentUpdate {
        /**
         * Output only. An optional user-provided description of the deployment after the current update has been applied.
         */
        description?: string | null;
        /**
         * Map of One Platform labels; provided by the client when the resource is created or updated. Specifically: Label keys must be between 1 and 63 characters long and must conform to the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0 and 63 characters long and must conform to the regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
         */
        labels?: Schema$DeploymentUpdateLabelEntry[];
        /**
         * Output only. URL of the manifest representing the update configuration of this deployment.
         */
        manifest?: string | null;
    }
    /**
     * Label object for DeploymentUpdate
     */
    export interface Schema$DeploymentUpdateLabelEntry {
        /**
         * Key of the label
         */
        key?: string | null;
        /**
         * Value of the label
         */
        value?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    export interface Schema$GlobalSetPolicyRequest {
        /**
         * Flatten Policy to create a backward compatible wire-format. Deprecated. Use 'policy' to specify bindings.
         */
        bindings?: Schema$Binding[];
        /**
         * Flatten Policy to create a backward compatible wire-format. Deprecated. Use 'policy' to specify the etag.
         */
        etag?: string | null;
        /**
         * REQUIRED: The complete policy to be applied to the 'resource'. The size of the policy is limited to a few 10s of KB. An empty policy is in general a valid policy but certain services (like Projects) might reject them.
         */
        policy?: Schema$Policy;
    }
    export interface Schema$ImportFile {
        /**
         * The contents of the file.
         */
        content?: string | null;
        /**
         * The name of the file.
         */
        name?: string | null;
    }
    export interface Schema$InstancesBulkInsertOperationMetadata {
        /**
         * Status information per location (location name is key). Example key: zones/us-central1-a
         */
        perLocationStatus?: {
            [key: string]: Schema$BulkInsertOperationStatus;
        } | null;
    }
    export interface Schema$Manifest {
        /**
         * Output only. The YAML configuration for this manifest.
         */
        config?: Schema$ConfigFile;
        /**
         * Output only. The fully-expanded configuration file, including any templates and references.
         */
        expandedConfig?: string | null;
        id?: string | null;
        /**
         * Output only. The imported files for this manifest.
         */
        imports?: Schema$ImportFile[];
        /**
         * Output only. Creation timestamp in RFC3339 text format.
         */
        insertTime?: string | null;
        /**
         * Output only. The YAML layout for this manifest.
         */
        layout?: string | null;
        /**
         * Output only. The computed size of the fully expanded manifest.
         */
        manifestSizeBytes?: string | null;
        /**
         * Output only. The size limit for expanded manifests in the project.
         */
        manifestSizeLimitBytes?: string | null;
        /**
         * Output only. The name of the manifest.
         */
        name?: string | null;
        /**
         * Output only. Self link for the manifest.
         */
        selfLink?: string | null;
    }
    /**
     * A response containing a partial list of manifests and a page token used to build the next request if the request has been truncated.
     */
    export interface Schema$ManifestsListResponse {
        /**
         * Output only. Manifests contained in this list response.
         */
        manifests?: Schema$Manifest[];
        /**
         * Output only. A token used to continue a truncated list request.
         */
        nextPageToken?: string | null;
    }
    /**
     * Represents an Operation resource. Google Compute Engine has three Operation resources: * [Global](/compute/docs/reference/rest/{$api_version\}/globalOperations) * [Regional](/compute/docs/reference/rest/{$api_version\}/regionOperations) * [Zonal](/compute/docs/reference/rest/{$api_version\}/zoneOperations) You can use an operation resource to manage asynchronous API requests. For more information, read Handling API responses. Operations can be global, regional or zonal. - For global operations, use the `globalOperations` resource. - For regional operations, use the `regionOperations` resource. - For zonal operations, use the `zoneOperations` resource. For more information, read Global, Regional, and Zonal Resources.
     */
    export interface Schema$Operation {
        /**
         * [Output Only] The value of `requestId` if you provided it in the request. Not present otherwise.
         */
        clientOperationId?: string | null;
        /**
         * [Deprecated] This field is deprecated.
         */
        creationTimestamp?: string | null;
        /**
         * [Output Only] A textual description of the operation, which is set when the operation is created.
         */
        description?: string | null;
        /**
         * [Output Only] The time that this operation was completed. This value is in RFC3339 text format.
         */
        endTime?: string | null;
        /**
         * [Output Only] If errors are generated during processing of the operation, this field will be populated.
         */
        error?: {
            errors?: Array<{
                code?: string;
                location?: string;
                message?: string;
            }>;
        } | null;
        /**
         * [Output Only] If the operation fails, this field contains the HTTP error message that was returned, such as `NOT FOUND`.
         */
        httpErrorMessage?: string | null;
        /**
         * [Output Only] If the operation fails, this field contains the HTTP error status code that was returned. For example, a `404` means the resource was not found.
         */
        httpErrorStatusCode?: number | null;
        /**
         * [Output Only] The unique identifier for the operation. This identifier is defined by the server.
         */
        id?: string | null;
        /**
         * [Output Only] The time that this operation was requested. This value is in RFC3339 text format.
         */
        insertTime?: string | null;
        instancesBulkInsertOperationMetadata?: Schema$InstancesBulkInsertOperationMetadata;
        /**
         * [Output Only] Type of the resource. Always `compute#operation` for Operation resources.
         */
        kind?: string | null;
        /**
         * [Output Only] Name of the operation.
         */
        name?: string | null;
        /**
         * [Output Only] An ID that represents a group of operations, such as when a group of operations results from a `bulkInsert` API request.
         */
        operationGroupId?: string | null;
        /**
         * [Output Only] The type of operation, such as `insert`, `update`, or `delete`, and so on.
         */
        operationType?: string | null;
        /**
         * [Output Only] An optional progress indicator that ranges from 0 to 100. There is no requirement that this be linear or support any granularity of operations. This should not be used to guess when the operation will be complete. This number should monotonically increase as the operation progresses.
         */
        progress?: number | null;
        /**
         * [Output Only] The URL of the region where the operation resides. Only applicable when performing regional operations.
         */
        region?: string | null;
        /**
         * [Output Only] Server-defined URL for the resource.
         */
        selfLink?: string | null;
        /**
         * [Output Only] If the operation is for projects.setCommonInstanceMetadata, this field will contain information on all underlying zonal actions and their state.
         */
        setCommonInstanceMetadataOperationMetadata?: Schema$SetCommonInstanceMetadataOperationMetadata;
        /**
         * [Output Only] The time that this operation was started by the server. This value is in RFC3339 text format.
         */
        startTime?: string | null;
        /**
         * [Output Only] The status of the operation, which can be one of the following: `PENDING`, `RUNNING`, or `DONE`.
         */
        status?: string | null;
        /**
         * [Output Only] An optional textual description of the current status of the operation.
         */
        statusMessage?: string | null;
        /**
         * [Output Only] The unique target ID, which identifies a specific incarnation of the target resource.
         */
        targetId?: string | null;
        /**
         * [Output Only] The URL of the resource that the operation modifies. For operations related to creating a snapshot, this points to the persistent disk that the snapshot was created from.
         */
        targetLink?: string | null;
        /**
         * [Output Only] User who requested the operation, for example: `<EMAIL>` or `alice_smith_identifier (global/workforcePools/example-com-us-employees)`.
         */
        user?: string | null;
        /**
         * [Output Only] If warning messages are generated during processing of the operation, this field will be populated.
         */
        warnings?: Array<{
            code?: string;
            data?: Array<{
                key?: string;
                value?: string;
            }>;
            message?: string;
        }> | null;
        /**
         * [Output Only] The URL of the zone where the operation resides. Only applicable when performing per-zone operations.
         */
        zone?: string | null;
    }
    /**
     * A response containing a partial list of operations and a page token used to build the next request if the request has been truncated.
     */
    export interface Schema$OperationsListResponse {
        /**
         * Output only. A token used to continue a truncated list request.
         */
        nextPageToken?: string | null;
        /**
         * Output only. Operations contained in this list response.
         */
        operations?: Schema$Operation[];
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    export interface Schema$Resource {
        /**
         * The Access Control Policy set on this resource.
         */
        accessControl?: Schema$ResourceAccessControl;
        /**
         * Output only. The evaluated properties of the resource with references expanded. Returned as serialized YAML.
         */
        finalProperties?: string | null;
        id?: string | null;
        /**
         * Output only. Creation timestamp in RFC3339 text format.
         */
        insertTime?: string | null;
        /**
         * Output only. URL of the manifest representing the current configuration of this resource.
         */
        manifest?: string | null;
        /**
         * Output only. The name of the resource as it appears in the YAML config.
         */
        name?: string | null;
        /**
         * Output only. The current properties of the resource before any references have been filled in. Returned as serialized YAML.
         */
        properties?: string | null;
        /**
         * Output only. The type of the resource, for example `compute.v1.instance`, or `cloudfunctions.v1beta1.function`.
         */
        type?: string | null;
        /**
         * Output only. If Deployment Manager is currently updating or previewing an update to this resource, the updated configuration appears here.
         */
        update?: Schema$ResourceUpdate;
        /**
         * Output only. Update timestamp in RFC3339 text format.
         */
        updateTime?: string | null;
        /**
         * Output only. The URL of the actual resource.
         */
        url?: string | null;
        /**
         * Output only. If warning messages are generated during processing of this resource, this field will be populated.
         */
        warnings?: Array<{
            code?: string;
            data?: Array<{
                key?: string;
                value?: string;
            }>;
            message?: string;
        }> | null;
    }
    /**
     * The access controls set on the resource.
     */
    export interface Schema$ResourceAccessControl {
        /**
         * The GCP IAM Policy to set on the resource.
         */
        gcpIamPolicy?: string | null;
    }
    /**
     * A response containing a partial list of resources and a page token used to build the next request if the request has been truncated.
     */
    export interface Schema$ResourcesListResponse {
        /**
         * A token used to continue a truncated list request.
         */
        nextPageToken?: string | null;
        /**
         * Resources contained in this list response.
         */
        resources?: Schema$Resource[];
    }
    export interface Schema$ResourceUpdate {
        /**
         * The Access Control Policy to set on this resource after updating the resource itself.
         */
        accessControl?: Schema$ResourceAccessControl;
        /**
         * Output only. If errors are generated during update of the resource, this field will be populated.
         */
        error?: {
            errors?: Array<{
                code?: string;
                location?: string;
                message?: string;
            }>;
        } | null;
        /**
         * Output only. The expanded properties of the resource with reference values expanded. Returned as serialized YAML.
         */
        finalProperties?: string | null;
        /**
         * Output only. The intent of the resource: `PREVIEW`, `UPDATE`, or `CANCEL`.
         */
        intent?: string | null;
        /**
         * Output only. URL of the manifest representing the update configuration of this resource.
         */
        manifest?: string | null;
        /**
         * Output only. The set of updated properties for this resource, before references are expanded. Returned as serialized YAML.
         */
        properties?: string | null;
        /**
         * Output only. The state of the resource.
         */
        state?: string | null;
        /**
         * Output only. If warning messages are generated during processing of this resource, this field will be populated.
         */
        warnings?: Array<{
            code?: string;
            data?: Array<{
                key?: string;
                value?: string;
            }>;
            message?: string;
        }> | null;
    }
    export interface Schema$SetCommonInstanceMetadataOperationMetadata {
        /**
         * [Output Only] The client operation id.
         */
        clientOperationId?: string | null;
        /**
         * [Output Only] Status information per location (location name is key). Example key: zones/us-central1-a
         */
        perLocationOperations?: {
            [key: string]: Schema$SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo;
        } | null;
    }
    export interface Schema$SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo {
        /**
         * [Output Only] If state is `ABANDONED` or `FAILED`, this field is populated.
         */
        error?: Schema$Status;
        /**
         * [Output Only] Status of the action, which can be one of the following: `PROPAGATING`, `PROPAGATED`, `ABANDONED`, `FAILED`, or `DONE`.
         */
        state?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    export interface Schema$TargetConfiguration {
        /**
         * The configuration to use for this deployment.
         */
        config?: Schema$ConfigFile;
        /**
         * Specifies any files to import for this configuration. This can be used to import templates or other files. For example, you might import a text file in order to use the file in a template.
         */
        imports?: Schema$ImportFile[];
    }
    export interface Schema$TestPermissionsRequest {
        /**
         * The set of permissions to check for the 'resource'. Permissions with wildcards (such as '*' or 'storage.*') are not allowed.
         */
        permissions?: string[] | null;
    }
    export interface Schema$TestPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A resource type supported by Deployment Manager.
     */
    export interface Schema$Type {
        id?: string | null;
        /**
         * Output only. Creation timestamp in RFC3339 text format.
         */
        insertTime?: string | null;
        /**
         * Name of the type.
         */
        name?: string | null;
        /**
         * Output only. The Operation that most recently ran, or is currently running, on this type.
         */
        operation?: Schema$Operation;
        /**
         * Output only. Server defined URL for the resource.
         */
        selfLink?: string | null;
    }
    /**
     * A response that returns all Types supported by Deployment Manager
     */
    export interface Schema$TypesListResponse {
        /**
         * A token used to continue a truncated list request.
         */
        nextPageToken?: string | null;
        /**
         * Output only. A list of resource types supported by Deployment Manager.
         */
        types?: Schema$Type[];
    }
    export class Resource$Deployments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Cancels and removes the preview currently associated with the deployment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancelPreview(params: Params$Resource$Deployments$Cancelpreview, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancelPreview(params?: Params$Resource$Deployments$Cancelpreview, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        cancelPreview(params: Params$Resource$Deployments$Cancelpreview, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancelPreview(params: Params$Resource$Deployments$Cancelpreview, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        cancelPreview(params: Params$Resource$Deployments$Cancelpreview, callback: BodyResponseCallback<Schema$Operation>): void;
        cancelPreview(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a deployment and all of the resources in the deployment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Deployments$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Deployments$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Deployments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Deployments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Deployments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets information about a specific deployment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Deployments$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Deployments$Get, options?: MethodOptions): GaxiosPromise<Schema$Deployment>;
        get(params: Params$Resource$Deployments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Deployments$Get, options: MethodOptions | BodyResponseCallback<Schema$Deployment>, callback: BodyResponseCallback<Schema$Deployment>): void;
        get(params: Params$Resource$Deployments$Get, callback: BodyResponseCallback<Schema$Deployment>): void;
        get(callback: BodyResponseCallback<Schema$Deployment>): void;
        /**
         * Gets the access control policy for a resource. May be empty if no such policy or resource exists.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Deployments$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Deployments$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Deployments$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Deployments$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Deployments$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Creates a deployment and all of the resources described by the deployment manifest.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        insert(params: Params$Resource$Deployments$Insert, options: StreamMethodOptions): GaxiosPromise<Readable>;
        insert(params?: Params$Resource$Deployments$Insert, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        insert(params: Params$Resource$Deployments$Insert, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        insert(params: Params$Resource$Deployments$Insert, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        insert(params: Params$Resource$Deployments$Insert, callback: BodyResponseCallback<Schema$Operation>): void;
        insert(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists all deployments for a given project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Deployments$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Deployments$List, options?: MethodOptions): GaxiosPromise<Schema$DeploymentsListResponse>;
        list(params: Params$Resource$Deployments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Deployments$List, options: MethodOptions | BodyResponseCallback<Schema$DeploymentsListResponse>, callback: BodyResponseCallback<Schema$DeploymentsListResponse>): void;
        list(params: Params$Resource$Deployments$List, callback: BodyResponseCallback<Schema$DeploymentsListResponse>): void;
        list(callback: BodyResponseCallback<Schema$DeploymentsListResponse>): void;
        /**
         * Patches a deployment and all of the resources described by the deployment manifest.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Deployments$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Deployments$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Deployments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Deployments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Deployments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Deployments$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Deployments$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Deployments$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Deployments$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Deployments$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Stops an ongoing operation. This does not roll back any work that has already been completed, but prevents any new work from being started.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Deployments$Stop, options: StreamMethodOptions): GaxiosPromise<Readable>;
        stop(params?: Params$Resource$Deployments$Stop, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        stop(params: Params$Resource$Deployments$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Deployments$Stop, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(params: Params$Resource$Deployments$Stop, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Returns permissions that a caller has on the specified resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Deployments$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Deployments$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Deployments$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Deployments$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestPermissionsResponse>, callback: BodyResponseCallback<Schema$TestPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Deployments$Testiampermissions, callback: BodyResponseCallback<Schema$TestPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestPermissionsResponse>): void;
        /**
         * Updates a deployment and all of the resources described by the deployment manifest.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        update(params: Params$Resource$Deployments$Update, options: StreamMethodOptions): GaxiosPromise<Readable>;
        update(params?: Params$Resource$Deployments$Update, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        update(params: Params$Resource$Deployments$Update, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        update(params: Params$Resource$Deployments$Update, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        update(params: Params$Resource$Deployments$Update, callback: BodyResponseCallback<Schema$Operation>): void;
        update(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Deployments$Cancelpreview extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeploymentsCancelPreviewRequest;
    }
    export interface Params$Resource$Deployments$Delete extends StandardParameters {
        /**
         * Sets the policy to use for deleting resources.
         */
        deletePolicy?: string;
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export interface Params$Resource$Deployments$Get extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export interface Params$Resource$Deployments$Getiampolicy extends StandardParameters {
        /**
         * Requested IAM Policy version.
         */
        optionsRequestedPolicyVersion?: number;
        /**
         * Project ID for this request.
         */
        project?: string;
        /**
         * Name or id of the resource for this request.
         */
        resource?: string;
    }
    export interface Params$Resource$Deployments$Insert extends StandardParameters {
        /**
         * Sets the policy to use for creating new resources.
         */
        createPolicy?: string;
        /**
         * If set to true, creates a deployment and creates "shell" resources but does not actually instantiate these resources. This allows you to preview what your deployment looks like. After previewing a deployment, you can deploy your resources by making a request with the `update()` method or you can use the `cancelPreview()` method to cancel the preview altogether. Note that the deployment will still exist after you cancel the preview and you must separately delete this deployment if you want to remove it.
         */
        preview?: boolean;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Deployment;
    }
    export interface Params$Resource$Deployments$List extends StandardParameters {
        /**
         * A filter expression that filters resources listed in the response. Most Compute resources support two types of filter expressions: expressions that support regular expressions and expressions that follow API improvement proposal AIP-160. These two types of filter expressions cannot be mixed in one request. If you want to use AIP-160, your expression must specify the field name, an operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The operator must be either `=`, `!=`, `\>`, `<`, `<=`, `\>=` or `:`. For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`. The `:*` comparison can be used to test whether a key has been defined. For example, to find all objects with `owner` label use: ``` labels.owner:* ``` You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ``` If you want to use a regular expression, use the `eq` (equal) or `ne` (not equal) operator against a single un-parenthesized expression with or without quotes or against multiple parenthesized expressions. Examples: `fieldname eq unquoted literal` `fieldname eq 'single quoted literal'` `fieldname eq "double quoted literal"` `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is interpreted as a regular expression using Google RE2 library syntax. The literal value must match the entire field. For example, to filter for instances that do not end with name "instance", you would use `name ne .*instance`. You cannot combine constraints on multiple fields using regular expressions.
         */
        filter?: string;
        /**
         * The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
         */
        maxResults?: number;
        /**
         * Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name. You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first. Currently, only sorting by `name` or `creationTimestamp desc` is supported.
         */
        orderBy?: string;
        /**
         * Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
         */
        pageToken?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export interface Params$Resource$Deployments$Patch extends StandardParameters {
        /**
         * Sets the policy to use for creating new resources.
         */
        createPolicy?: string;
        /**
         * Sets the policy to use for deleting resources.
         */
        deletePolicy?: string;
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * If set to true, updates the deployment and creates and updates the "shell" resources but does not actually alter or instantiate these resources. This allows you to preview what your deployment will look like. You can use this intent to preview how an update would affect your deployment. You must provide a `target.config` with a configuration if this is set to true. After previewing a deployment, you can deploy your resources by making a request with the `update()` or you can `cancelPreview()` to remove the preview altogether. Note that the deployment will still exist after you cancel the preview and you must separately delete this deployment if you want to remove it.
         */
        preview?: boolean;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Deployment;
    }
    export interface Params$Resource$Deployments$Setiampolicy extends StandardParameters {
        /**
         * Project ID for this request.
         */
        project?: string;
        /**
         * Name or id of the resource for this request.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GlobalSetPolicyRequest;
    }
    export interface Params$Resource$Deployments$Stop extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeploymentsStopRequest;
    }
    export interface Params$Resource$Deployments$Testiampermissions extends StandardParameters {
        /**
         * Project ID for this request.
         */
        project?: string;
        /**
         * Name or id of the resource for this request.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestPermissionsRequest;
    }
    export interface Params$Resource$Deployments$Update extends StandardParameters {
        /**
         * Sets the policy to use for creating new resources.
         */
        createPolicy?: string;
        /**
         * Sets the policy to use for deleting resources.
         */
        deletePolicy?: string;
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * If set to true, updates the deployment and creates and updates the "shell" resources but does not actually alter or instantiate these resources. This allows you to preview what your deployment will look like. You can use this intent to preview how an update would affect your deployment. You must provide a `target.config` with a configuration if this is set to true. After previewing a deployment, you can deploy your resources by making a request with the `update()` or you can `cancelPreview()` to remove the preview altogether. Note that the deployment will still exist after you cancel the preview and you must separately delete this deployment if you want to remove it.
         */
        preview?: boolean;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Deployment;
    }
    export class Resource$Manifests {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a specific manifest.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Manifests$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Manifests$Get, options?: MethodOptions): GaxiosPromise<Schema$Manifest>;
        get(params: Params$Resource$Manifests$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Manifests$Get, options: MethodOptions | BodyResponseCallback<Schema$Manifest>, callback: BodyResponseCallback<Schema$Manifest>): void;
        get(params: Params$Resource$Manifests$Get, callback: BodyResponseCallback<Schema$Manifest>): void;
        get(callback: BodyResponseCallback<Schema$Manifest>): void;
        /**
         * Lists all manifests for a given deployment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Manifests$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Manifests$List, options?: MethodOptions): GaxiosPromise<Schema$ManifestsListResponse>;
        list(params: Params$Resource$Manifests$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Manifests$List, options: MethodOptions | BodyResponseCallback<Schema$ManifestsListResponse>, callback: BodyResponseCallback<Schema$ManifestsListResponse>): void;
        list(params: Params$Resource$Manifests$List, callback: BodyResponseCallback<Schema$ManifestsListResponse>): void;
        list(callback: BodyResponseCallback<Schema$ManifestsListResponse>): void;
    }
    export interface Params$Resource$Manifests$Get extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The name of the manifest for this request.
         */
        manifest?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export interface Params$Resource$Manifests$List extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * A filter expression that filters resources listed in the response. Most Compute resources support two types of filter expressions: expressions that support regular expressions and expressions that follow API improvement proposal AIP-160. These two types of filter expressions cannot be mixed in one request. If you want to use AIP-160, your expression must specify the field name, an operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The operator must be either `=`, `!=`, `\>`, `<`, `<=`, `\>=` or `:`. For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`. The `:*` comparison can be used to test whether a key has been defined. For example, to find all objects with `owner` label use: ``` labels.owner:* ``` You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ``` If you want to use a regular expression, use the `eq` (equal) or `ne` (not equal) operator against a single un-parenthesized expression with or without quotes or against multiple parenthesized expressions. Examples: `fieldname eq unquoted literal` `fieldname eq 'single quoted literal'` `fieldname eq "double quoted literal"` `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is interpreted as a regular expression using Google RE2 library syntax. The literal value must match the entire field. For example, to filter for instances that do not end with name "instance", you would use `name ne .*instance`. You cannot combine constraints on multiple fields using regular expressions.
         */
        filter?: string;
        /**
         * The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
         */
        maxResults?: number;
        /**
         * Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name. You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first. Currently, only sorting by `name` or `creationTimestamp desc` is supported.
         */
        orderBy?: string;
        /**
         * Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
         */
        pageToken?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a specific operation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists all operations for a project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$OperationsListResponse>;
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$OperationsListResponse>, callback: BodyResponseCallback<Schema$OperationsListResponse>): void;
        list(params: Params$Resource$Operations$List, callback: BodyResponseCallback<Schema$OperationsListResponse>): void;
        list(callback: BodyResponseCallback<Schema$OperationsListResponse>): void;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation for this request.
         */
        operation?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export interface Params$Resource$Operations$List extends StandardParameters {
        /**
         * A filter expression that filters resources listed in the response. Most Compute resources support two types of filter expressions: expressions that support regular expressions and expressions that follow API improvement proposal AIP-160. These two types of filter expressions cannot be mixed in one request. If you want to use AIP-160, your expression must specify the field name, an operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The operator must be either `=`, `!=`, `\>`, `<`, `<=`, `\>=` or `:`. For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`. The `:*` comparison can be used to test whether a key has been defined. For example, to find all objects with `owner` label use: ``` labels.owner:* ``` You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ``` If you want to use a regular expression, use the `eq` (equal) or `ne` (not equal) operator against a single un-parenthesized expression with or without quotes or against multiple parenthesized expressions. Examples: `fieldname eq unquoted literal` `fieldname eq 'single quoted literal'` `fieldname eq "double quoted literal"` `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is interpreted as a regular expression using Google RE2 library syntax. The literal value must match the entire field. For example, to filter for instances that do not end with name "instance", you would use `name ne .*instance`. You cannot combine constraints on multiple fields using regular expressions.
         */
        filter?: string;
        /**
         * The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
         */
        maxResults?: number;
        /**
         * Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name. You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first. Currently, only sorting by `name` or `creationTimestamp desc` is supported.
         */
        orderBy?: string;
        /**
         * Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
         */
        pageToken?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export class Resource$Resources {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a single resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Resources$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Resources$Get, options?: MethodOptions): GaxiosPromise<Schema$Resource>;
        get(params: Params$Resource$Resources$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Resources$Get, options: MethodOptions | BodyResponseCallback<Schema$Resource>, callback: BodyResponseCallback<Schema$Resource>): void;
        get(params: Params$Resource$Resources$Get, callback: BodyResponseCallback<Schema$Resource>): void;
        get(callback: BodyResponseCallback<Schema$Resource>): void;
        /**
         * Lists all resources in a given deployment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Resources$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Resources$List, options?: MethodOptions): GaxiosPromise<Schema$ResourcesListResponse>;
        list(params: Params$Resource$Resources$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Resources$List, options: MethodOptions | BodyResponseCallback<Schema$ResourcesListResponse>, callback: BodyResponseCallback<Schema$ResourcesListResponse>): void;
        list(params: Params$Resource$Resources$List, callback: BodyResponseCallback<Schema$ResourcesListResponse>): void;
        list(callback: BodyResponseCallback<Schema$ResourcesListResponse>): void;
    }
    export interface Params$Resource$Resources$Get extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
        /**
         * The name of the resource for this request.
         */
        resource?: string;
    }
    export interface Params$Resource$Resources$List extends StandardParameters {
        /**
         * The name of the deployment for this request.
         */
        deployment?: string;
        /**
         * A filter expression that filters resources listed in the response. Most Compute resources support two types of filter expressions: expressions that support regular expressions and expressions that follow API improvement proposal AIP-160. These two types of filter expressions cannot be mixed in one request. If you want to use AIP-160, your expression must specify the field name, an operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The operator must be either `=`, `!=`, `\>`, `<`, `<=`, `\>=` or `:`. For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`. The `:*` comparison can be used to test whether a key has been defined. For example, to find all objects with `owner` label use: ``` labels.owner:* ``` You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ``` If you want to use a regular expression, use the `eq` (equal) or `ne` (not equal) operator against a single un-parenthesized expression with or without quotes or against multiple parenthesized expressions. Examples: `fieldname eq unquoted literal` `fieldname eq 'single quoted literal'` `fieldname eq "double quoted literal"` `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is interpreted as a regular expression using Google RE2 library syntax. The literal value must match the entire field. For example, to filter for instances that do not end with name "instance", you would use `name ne .*instance`. You cannot combine constraints on multiple fields using regular expressions.
         */
        filter?: string;
        /**
         * The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
         */
        maxResults?: number;
        /**
         * Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name. You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first. Currently, only sorting by `name` or `creationTimestamp desc` is supported.
         */
        orderBy?: string;
        /**
         * Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
         */
        pageToken?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export class Resource$Types {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all resource types for Deployment Manager.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Types$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Types$List, options?: MethodOptions): GaxiosPromise<Schema$TypesListResponse>;
        list(params: Params$Resource$Types$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Types$List, options: MethodOptions | BodyResponseCallback<Schema$TypesListResponse>, callback: BodyResponseCallback<Schema$TypesListResponse>): void;
        list(params: Params$Resource$Types$List, callback: BodyResponseCallback<Schema$TypesListResponse>): void;
        list(callback: BodyResponseCallback<Schema$TypesListResponse>): void;
    }
    export interface Params$Resource$Types$List extends StandardParameters {
        /**
         * A filter expression that filters resources listed in the response. Most Compute resources support two types of filter expressions: expressions that support regular expressions and expressions that follow API improvement proposal AIP-160. These two types of filter expressions cannot be mixed in one request. If you want to use AIP-160, your expression must specify the field name, an operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The operator must be either `=`, `!=`, `\>`, `<`, `<=`, `\>=` or `:`. For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`. The `:*` comparison can be used to test whether a key has been defined. For example, to find all objects with `owner` label use: ``` labels.owner:* ``` You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ``` If you want to use a regular expression, use the `eq` (equal) or `ne` (not equal) operator against a single un-parenthesized expression with or without quotes or against multiple parenthesized expressions. Examples: `fieldname eq unquoted literal` `fieldname eq 'single quoted literal'` `fieldname eq "double quoted literal"` `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is interpreted as a regular expression using Google RE2 library syntax. The literal value must match the entire field. For example, to filter for instances that do not end with name "instance", you would use `name ne .*instance`. You cannot combine constraints on multiple fields using regular expressions.
         */
        filter?: string;
        /**
         * The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
         */
        maxResults?: number;
        /**
         * Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name. You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first. Currently, only sorting by `name` or `creationTimestamp desc` is supported.
         */
        orderBy?: string;
        /**
         * Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
         */
        pageToken?: string;
        /**
         * The project ID for this request.
         */
        project?: string;
    }
    export {};
}
