{"version": 3, "file": "iamService.js", "sourceRoot": "", "sources": ["../../src/iamService.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;AAC5E,iEAAiE;AACjE,qDAAqD;;;AAKrD,mDAA8C;AAG9C,iDAAiD;AACjD,uEAAuE;AAEvE,uCAAuC;AAEvC,IAAI,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;AACpD,yDAA0D;AAE1D;;;GAGG;AACH,MAAa,SAAS;IAYpB,YACE,OAAwC;IACxC,8DAA8D;IAC9D,OAAsB;QAdhB,gBAAW,GAAG,KAAK,CAAC;QAM5B,gBAAW,GAAgB,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAC,CAAC;QACnE,kBAAa,GAA+B,EAAE,CAAC;QAS7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,4DAA4D;QAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CACxB;YACE,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,EACD,OAAO,CAC6B,CAAC;QACvC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;QACrD,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,WAAgC,CAAC,MAAM,CAAC;QAC5D,8CAA8C;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,gEAAgE;QAChE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAEzB,sCAAsC;QACtC,MAAM,YAAY,GAAG,CAAC,OAAO,OAAO,EAAE,EAAE,SAAS,OAAO,EAAE,CAAC,CAAC;QAC5D,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;YAC5D,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,8BAA8B;QAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACtD,uDAAuD;QACvD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,iBAAiB,CACxC,yBAAyB,EACzB,WAA+B,EAC/B,IAAK,CAAC,YAAY,IAAI,EAAE,EACxB,EAAC,mBAAmB,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAC9C,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;;;;;OAUG;IACH,UAAU;QACR,yEAAyE;QACzE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;QACD,sCAAsC;QACtC,2BAA2B;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1C,IAAI,CAAC,KAAK,CAAC,QAAQ;YACjB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC;YACvD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EACxC,IAAI,CAAC,KAAK,CAC8B,CAAC;QAC3C,6DAA6D;QAC7D,0CAA0C;QAC1C,MAAM,oBAAoB,GAAG;YAC3B,cAAc;YACd,cAAc;YACd,oBAAoB;SACrB,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,oBAAoB,EAAE,CAAC;YAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAC9C,IAAI,CAAC,EAAE,CACL,CAAC,GAAG,IAAe,EAAE,EAAE;gBACrB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,OAAO,OAAO,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC;gBAC/D,CAAC;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC,EACH,CAAC,GAA6B,EAAE,EAAE,CAAC,GAAG,EAAE;gBACtC,MAAM,GAAG,CAAC;YACZ,CAAC,CACF,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAA,6BAAa,EAC5C,gBAAgB,EAChB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAClC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,KAAK,WAAW;QACpB,OAAO,yBAAyB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,MAAM,KAAK,WAAW;QACpB,OAAO,yBAAyB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,KAAK,IAAI;QACb,OAAO,GAAG,CAAC;IACb,CAAC;IACD;;;OAGG;IACH,MAAM,KAAK,MAAM;QACf,OAAO;YACL,gDAAgD;YAChD,0CAA0C;SAC3C,CAAC;IACJ,CAAC;IASD,YAAY,CAAC,QAA4B;QACvC,IAAI,IAAI,CAAC,IAAI,IAAI,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAS,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAuBD,YAAY,CACV,OAAiD,EACjD,iBAMK,EACL,QAIC;QAED,IAAI,OAAwB,CAAC;QAC7B,IAAI,iBAAiB,YAAY,QAAQ,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpE,QAAQ,GAAG,iBAIV,CAAC;YACF,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,iBAAoC,CAAC;QACjD,CAAC;QACD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAuBD,YAAY,CACV,OAAiD,EACjD,iBAMK,EACL,QAIC;QAED,IAAI,OAAwB,CAAC;QAC7B,IAAI,iBAAiB,YAAY,QAAQ,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpE,QAAQ,GAAG,iBAIV,CAAC;YACF,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,iBAAoC,CAAC;QACjD,CAAC;QACD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAsBD,kBAAkB,CAChB,OAAuD,EACvD,iBAMK,EACL,QAIC;QAED,IAAI,OAAwB,CAAC;QAC7B,IAAI,iBAAiB,YAAY,QAAQ,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpE,QAAQ,GAAG,iBAIV,CAAC;YACF,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,iBAAoC,CAAC;QACjD,CAAC;QACD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC;gBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF;AArWD,8BAqWC"}