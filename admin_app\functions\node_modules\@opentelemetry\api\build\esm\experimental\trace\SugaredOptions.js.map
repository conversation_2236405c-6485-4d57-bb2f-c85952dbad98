{"version": 3, "file": "SugaredOptions.js", "sourceRoot": "", "sources": ["../../../../src/experimental/trace/SugaredOptions.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span, SpanOptions } from '../../';\n\n/**\n * Options needed for span creation\n */\nexport interface SugaredSpanOptions extends SpanOptions {\n  /**\n   * function to overwrite default exception behavior to record the exception. No exceptions should be thrown in the function.\n   * @param e Error which triggered this exception\n   * @param span current span from context\n   */\n  onException?: (e: Error, span: Span) => void;\n}\n"]}