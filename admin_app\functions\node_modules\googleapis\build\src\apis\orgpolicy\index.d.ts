/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { orgpolicy_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof orgpolicy_v2.Orgpolicy;
};
export declare function orgpolicy(version: 'v2'): orgpolicy_v2.Orgpolicy;
export declare function orgpolicy(options: orgpolicy_v2.Options): orgpolicy_v2.Orgpolicy;
declare const auth: AuthPlus;
export { auth };
export { orgpolicy_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
