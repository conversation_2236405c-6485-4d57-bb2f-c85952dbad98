/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { searchads360_v0 } from './v0';
export declare const VERSIONS: {
    v0: typeof searchads360_v0.Searchads360;
};
export declare function searchads360(version: 'v0'): searchads360_v0.Searchads360;
export declare function searchads360(options: searchads360_v0.Options): searchads360_v0.Searchads360;
declare const auth: AuthPlus;
export { auth };
export { searchads360_v0 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
