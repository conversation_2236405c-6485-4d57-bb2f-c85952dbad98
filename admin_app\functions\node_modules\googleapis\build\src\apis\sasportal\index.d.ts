/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { sasportal_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof sasportal_v1alpha1.Sasportal;
};
export declare function sasportal(version: 'v1alpha1'): sasportal_v1alpha1.Sasportal;
export declare function sasportal(options: sasportal_v1alpha1.Options): sasportal_v1alpha1.Sasportal;
declare const auth: AuthPlus;
export { auth };
export { sasportal_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
