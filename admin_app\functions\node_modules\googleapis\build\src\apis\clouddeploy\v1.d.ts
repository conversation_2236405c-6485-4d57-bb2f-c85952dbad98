/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace clouddeploy_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Deploy API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const clouddeploy = google.clouddeploy('v1');
     * ```
     */
    export class Clouddeploy {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * The request object used by `AbandonRelease`.
     */
    export interface Schema$AbandonReleaseRequest {
    }
    /**
     * The response object for `AbandonRelease`.
     */
    export interface Schema$AbandonReleaseResponse {
    }
    /**
     * An advanceChildRollout Job.
     */
    export interface Schema$AdvanceChildRolloutJob {
    }
    /**
     * AdvanceChildRolloutJobRun contains information specific to a advanceChildRollout `JobRun`.
     */
    export interface Schema$AdvanceChildRolloutJobRun {
        /**
         * Output only. Name of the `ChildRollout`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/a-z{0,62\}.
         */
        rollout?: string | null;
        /**
         * Output only. the ID of the ChildRollout's Phase.
         */
        rolloutPhaseId?: string | null;
    }
    /**
     * The request object used by `AdvanceRollout`.
     */
    export interface Schema$AdvanceRolloutRequest {
        /**
         * Required. The phase ID to advance the `Rollout` to.
         */
        phaseId?: string | null;
    }
    /**
     * The response object from `AdvanceRollout`.
     */
    export interface Schema$AdvanceRolloutResponse {
    }
    /**
     * Information specifying an Anthos Cluster.
     */
    export interface Schema$AnthosCluster {
        /**
         * Membership of the GKE Hub-registered cluster to which to apply the Skaffold configuration. Format is `projects/{project\}/locations/{location\}/memberships/{membership_name\}`.
         */
        membership?: string | null;
    }
    /**
     * The request object used by `ApproveRollout`.
     */
    export interface Schema$ApproveRolloutRequest {
        /**
         * Required. True = approve; false = reject
         */
        approved?: boolean | null;
    }
    /**
     * The response object from `ApproveRollout`.
     */
    export interface Schema$ApproveRolloutResponse {
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/automation" Platform Log event that describes the Automation related events.
     */
    export interface Schema$AutomationEvent {
        /**
         * The name of the `AutomationRun`.
         */
        automation?: string | null;
        /**
         * Debug message for when there is an update on the AutomationRun. Provides further details about the resource creation or state change.
         */
        message?: string | null;
        /**
         * Unique identifier of the `DeliveryPipeline`.
         */
        pipelineUid?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/automation_run" Platform Log event that describes the AutomationRun related events.
     */
    export interface Schema$AutomationRunEvent {
        /**
         * Identifier of the `Automation`.
         */
        automationId?: string | null;
        /**
         * The name of the `AutomationRun`.
         */
        automationRun?: string | null;
        /**
         * ID of the `Target` to which the `AutomationRun` is created.
         */
        destinationTargetId?: string | null;
        /**
         * Debug message for when there is an update on the AutomationRun. Provides further details about the resource creation or state change.
         */
        message?: string | null;
        /**
         * Unique identifier of the `DeliveryPipeline`.
         */
        pipelineUid?: string | null;
        /**
         * Identifier of the `Automation` rule.
         */
        ruleId?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * Description of an a image to use during Skaffold rendering.
     */
    export interface Schema$BuildArtifact {
        /**
         * Image name in Skaffold configuration.
         */
        image?: string | null;
        /**
         * Image tag to use. This will generally be the full path to an image, such as "gcr.io/my-project/busybox:1.2.3" or "gcr.io/my-project/busybox@sha256:abc123".
         */
        tag?: string | null;
    }
    /**
     * Canary represents the canary deployment strategy.
     */
    export interface Schema$Canary {
        /**
         * Configures the progressive based deployment for a Target.
         */
        canaryDeployment?: Schema$CanaryDeployment;
        /**
         * Configures the progressive based deployment for a Target, but allows customizing at the phase level where a phase represents each of the percentage deployments.
         */
        customCanaryDeployment?: Schema$CustomCanaryDeployment;
        /**
         * Optional. Runtime specific configurations for the deployment strategy. The runtime configuration is used to determine how Cloud Deploy will split traffic to enable a progressive deployment.
         */
        runtimeConfig?: Schema$RuntimeConfig;
    }
    /**
     * CanaryDeployment represents the canary deployment configuration
     */
    export interface Schema$CanaryDeployment {
        /**
         * Required. The percentage based deployments that will occur as a part of a `Rollout`. List is expected in ascending order and each integer n is 0 <= n < 100.
         */
        percentages?: number[] | null;
        /**
         * Optional. Configuration for the postdeploy job of the last phase. If this is not configured, there will be no postdeploy job for this phase.
         */
        postdeploy?: Schema$Postdeploy;
        /**
         * Optional. Configuration for the predeploy job of the first phase. If this is not configured, there will be no predeploy job for this phase.
         */
        predeploy?: Schema$Predeploy;
        /**
         * Whether to run verify tests after each percentage deployment.
         */
        verify?: boolean | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * The request object used by `CancelRollout`.
     */
    export interface Schema$CancelRolloutRequest {
    }
    /**
     * The response object from `CancelRollout`.
     */
    export interface Schema$CancelRolloutResponse {
    }
    /**
     * ChildRollouts job composition
     */
    export interface Schema$ChildRolloutJobs {
        /**
         * Output only. List of AdvanceChildRolloutJobs
         */
        advanceRolloutJobs?: Schema$Job[];
        /**
         * Output only. List of CreateChildRolloutJobs
         */
        createRolloutJobs?: Schema$Job[];
    }
    /**
     * CloudRunConfig contains the Cloud Run runtime configuration.
     */
    export interface Schema$CloudRunConfig {
        /**
         * Whether Cloud Deploy should update the traffic stanza in a Cloud Run Service on the user's behalf to facilitate traffic splitting. This is required to be true for CanaryDeployments, but optional for CustomCanaryDeployments.
         */
        automaticTrafficControl?: boolean | null;
    }
    /**
     * Information specifying where to deploy a Cloud Run Service.
     */
    export interface Schema$CloudRunLocation {
        /**
         * Required. The location for the Cloud Run Service. Format must be `projects/{project\}/locations/{location\}`.
         */
        location?: string | null;
    }
    /**
     * CloudRunMetadata contains information from a Cloud Run deployment.
     */
    export interface Schema$CloudRunMetadata {
        /**
         * Output only. The Cloud Run Revision id associated with a `Rollout`.
         */
        revision?: string | null;
        /**
         * Output only. The name of the Cloud Run Service that is associated with a `Rollout`. Format is projects/{project\}/locations/{location\}/services/{service\}.
         */
        service?: string | null;
        /**
         * Output only. The Cloud Run Service urls that are associated with a `Rollout`.
         */
        serviceUrls?: string[] | null;
    }
    /**
     * CloudRunRenderMetadata contains Cloud Run information associated with a `Release` render.
     */
    export interface Schema$CloudRunRenderMetadata {
        /**
         * Output only. The name of the Cloud Run Service in the rendered manifest. Format is projects/{project\}/locations/{location\}/services/{service\}.
         */
        service?: string | null;
    }
    /**
     * Service-wide configuration.
     */
    export interface Schema$Config {
        /**
         * Default Skaffold version that is assigned when a Release is created without specifying a Skaffold version.
         */
        defaultSkaffoldVersion?: string | null;
        /**
         * Name of the configuration.
         */
        name?: string | null;
        /**
         * All supported versions of Skaffold.
         */
        supportedVersions?: Schema$SkaffoldVersion[];
    }
    /**
     * A createChildRollout Job.
     */
    export interface Schema$CreateChildRolloutJob {
    }
    /**
     * CreateChildRolloutJobRun contains information specific to a createChildRollout `JobRun`.
     */
    export interface Schema$CreateChildRolloutJobRun {
        /**
         * Output only. Name of the `ChildRollout`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/a-z{0,62\}.
         */
        rollout?: string | null;
        /**
         * Output only. The ID of the childRollout Phase initiated by this JobRun.
         */
        rolloutPhaseId?: string | null;
    }
    /**
     * CustomCanaryDeployment represents the custom canary deployment configuration.
     */
    export interface Schema$CustomCanaryDeployment {
        /**
         * Required. Configuration for each phase in the canary deployment in the order executed.
         */
        phaseConfigs?: Schema$PhaseConfig[];
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * Execution using the default Cloud Build pool.
     */
    export interface Schema$DefaultPool {
        /**
         * Optional. Cloud Storage location where execution outputs should be stored. This can either be a bucket ("gs://my-bucket") or a path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a default bucket located in the same region will be used.
         */
        artifactStorage?: string | null;
        /**
         * Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) will be used.
         */
        serviceAccount?: string | null;
    }
    /**
     * A `DeliveryPipeline` resource in the Cloud Deploy API. A `DeliveryPipeline` defines a pipeline through which a Skaffold configuration can progress.
     */
    export interface Schema$DeliveryPipeline {
        /**
         * User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Information around the state of the Delivery Pipeline.
         */
        condition?: Schema$PipelineCondition;
        /**
         * Output only. Time at which the pipeline was created.
         */
        createTime?: string | null;
        /**
         * Description of the `DeliveryPipeline`. Max length is 255 characters.
         */
        description?: string | null;
        /**
         * This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Name of the `DeliveryPipeline`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/a-z{0,62\}.
         */
        name?: string | null;
        /**
         * SerialPipeline defines a sequential set of stages for a `DeliveryPipeline`.
         */
        serialPipeline?: Schema$SerialPipeline;
        /**
         * When suspended, no new releases or rollouts can be created, but in-progress ones will complete.
         */
        suspended?: boolean | null;
        /**
         * Output only. Unique identifier of the `DeliveryPipeline`.
         */
        uid?: string | null;
        /**
         * Output only. Most recent time at which the pipeline was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/deliverypipeline_notification" Platform Log event that describes the failure to send delivery pipeline status change Pub/Sub notification.
     */
    export interface Schema$DeliveryPipelineNotificationEvent {
        /**
         * The name of the `Delivery Pipeline`.
         */
        deliveryPipeline?: string | null;
        /**
         * Debug message for when a notification fails to send.
         */
        message?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * The artifacts produced by a deploy operation.
     */
    export interface Schema$DeployArtifact {
        /**
         * Output only. URI of a directory containing the artifacts. All paths are relative to this location.
         */
        artifactUri?: string | null;
        /**
         * Output only. File paths of the manifests applied during the deploy operation relative to the URI.
         */
        manifestPaths?: string[] | null;
    }
    /**
     * A deploy Job.
     */
    export interface Schema$DeployJob {
    }
    /**
     * DeployJobRun contains information specific to a deploy `JobRun`.
     */
    export interface Schema$DeployJobRun {
        /**
         * Output only. The artifact of a deploy job run, if available.
         */
        artifact?: Schema$DeployArtifact;
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to deploy. Format is projects/{project\}/locations/{location\}/builds/{build\}.
         */
        build?: string | null;
        /**
         * Output only. The reason the deploy failed. This will always be unspecified while the deploy is in progress or if it succeeded.
         */
        failureCause?: string | null;
        /**
         * Output only. Additional information about the deploy failure, if available.
         */
        failureMessage?: string | null;
        /**
         * Output only. Metadata containing information about the deploy job run.
         */
        metadata?: Schema$DeployJobRunMetadata;
    }
    /**
     * DeployJobRunMetadata surfaces information associated with a `DeployJobRun` to the user.
     */
    export interface Schema$DeployJobRunMetadata {
        /**
         * Output only. The name of the Cloud Run Service that is associated with a `DeployJobRun`.
         */
        cloudRun?: Schema$CloudRunMetadata;
    }
    /**
     * Deployment job composition.
     */
    export interface Schema$DeploymentJobs {
        /**
         * Output only. The deploy Job. This is the deploy job in the phase.
         */
        deployJob?: Schema$Job;
        /**
         * Output only. The postdeploy Job, which is the last job on the phase.
         */
        postdeployJob?: Schema$Job;
        /**
         * Output only. The predeploy Job, which is the first job on the phase.
         */
        predeployJob?: Schema$Job;
        /**
         * Output only. The verify Job. Runs after a deploy if the deploy succeeds.
         */
        verifyJob?: Schema$Job;
    }
    /**
     * DeployParameters contains deploy parameters information.
     */
    export interface Schema$DeployParameters {
        /**
         * Optional. Deploy parameters are applied to targets with match labels. If unspecified, deploy parameters are applied to all targets (including child targets of a multi-target).
         */
        matchTargetLabels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Values are deploy parameters in key-value pairs.
         */
        values?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Configuration of the environment to use when calling Skaffold.
     */
    export interface Schema$ExecutionConfig {
        /**
         * Optional. Cloud Storage location in which to store execution outputs. This can either be a bucket ("gs://my-bucket") or a path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a default bucket located in the same region will be used.
         */
        artifactStorage?: string | null;
        /**
         * Optional. Use default Cloud Build pool.
         */
        defaultPool?: Schema$DefaultPool;
        /**
         * Optional. Execution timeout for a Cloud Build Execution. This must be between 10m and 24h in seconds format. If unspecified, a default timeout of 1h is used.
         */
        executionTimeout?: string | null;
        /**
         * Optional. Use private Cloud Build pool.
         */
        privatePool?: Schema$PrivatePool;
        /**
         * Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) is used.
         */
        serviceAccount?: string | null;
        /**
         * Required. Usages when this configuration should be applied.
         */
        usages?: string[] | null;
        /**
         * Optional. The resource name of the `WorkerPool`, with the format `projects/{project\}/locations/{location\}/workerPools/{worker_pool\}`. If this optional field is unspecified, the default Cloud Build pool will be used.
         */
        workerPool?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Information about the Kubernetes Gateway API service mesh configuration.
     */
    export interface Schema$GatewayServiceMesh {
        /**
         * Required. Name of the Kubernetes Deployment whose traffic is managed by the specified HTTPRoute and Service.
         */
        deployment?: string | null;
        /**
         * Required. Name of the Gateway API HTTPRoute.
         */
        httpRoute?: string | null;
        /**
         * Optional. The time to wait for route updates to propagate. The maximum configurable time is 3 hours, in seconds format. If unspecified, there is no wait time.
         */
        routeUpdateWaitTime?: string | null;
        /**
         * Required. Name of the Kubernetes Service.
         */
        service?: string | null;
    }
    /**
     * Information specifying a GKE Cluster.
     */
    export interface Schema$GkeCluster {
        /**
         * Information specifying a GKE Cluster. Format is `projects/{project_id\}/locations/{location_id\}/clusters/{cluster_id\}.
         */
        cluster?: string | null;
        /**
         * Optional. If true, `cluster` is accessed using the private IP address of the control plane endpoint. Otherwise, the default IP address of the control plane endpoint is used. The default IP address is the private IP address for clusters with private control-plane endpoints and the public IP address otherwise. Only specify this option when `cluster` is a [private GKE cluster](https://cloud.google.com/kubernetes-engine/docs/concepts/private-cluster-concept).
         */
        internalIp?: boolean | null;
    }
    /**
     * The request object used by `IgnoreJob`.
     */
    export interface Schema$IgnoreJobRequest {
        /**
         * Required. The job ID for the Job to ignore.
         */
        jobId?: string | null;
        /**
         * Required. The phase ID the Job to ignore belongs to.
         */
        phaseId?: string | null;
    }
    /**
     * The response object from `IgnoreJob`.
     */
    export interface Schema$IgnoreJobResponse {
    }
    /**
     * Job represents an operation for a `Rollout`.
     */
    export interface Schema$Job {
        /**
         * Output only. An advanceChildRollout Job.
         */
        advanceChildRolloutJob?: Schema$AdvanceChildRolloutJob;
        /**
         * Output only. A createChildRollout Job.
         */
        createChildRolloutJob?: Schema$CreateChildRolloutJob;
        /**
         * Output only. A deploy Job.
         */
        deployJob?: Schema$DeployJob;
        /**
         * Output only. The ID of the Job.
         */
        id?: string | null;
        /**
         * Output only. The name of the `JobRun` responsible for the most recent invocation of this Job.
         */
        jobRun?: string | null;
        /**
         * Output only. A postdeploy Job.
         */
        postdeployJob?: Schema$PostdeployJob;
        /**
         * Output only. A predeploy Job.
         */
        predeployJob?: Schema$PredeployJob;
        /**
         * Output only. Additional information on why the Job was skipped, if available.
         */
        skipMessage?: string | null;
        /**
         * Output only. The current state of the Job.
         */
        state?: string | null;
        /**
         * Output only. A verify Job.
         */
        verifyJob?: Schema$VerifyJob;
    }
    /**
     * A `JobRun` resource in the Cloud Deploy API. A `JobRun` contains information of a single `Rollout` job evaluation.
     */
    export interface Schema$JobRun {
        /**
         * Output only. Information specific to an advanceChildRollout `JobRun`
         */
        advanceChildRolloutJobRun?: Schema$AdvanceChildRolloutJobRun;
        /**
         * Output only. Information specific to a createChildRollout `JobRun`.
         */
        createChildRolloutJobRun?: Schema$CreateChildRolloutJobRun;
        /**
         * Output only. Time at which the `JobRun` was created.
         */
        createTime?: string | null;
        /**
         * Output only. Information specific to a deploy `JobRun`.
         */
        deployJobRun?: Schema$DeployJobRun;
        /**
         * Output only. Time at which the `JobRun` ended.
         */
        endTime?: string | null;
        /**
         * Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Output only. ID of the `Rollout` job this `JobRun` corresponds to.
         */
        jobId?: string | null;
        /**
         * Optional. Name of the `JobRun`. Format is projects/{project\}/locations/{location\}/ deliveryPipelines/{deliveryPipeline\}/releases/{releases\}/rollouts/ {rollouts\}/jobRuns/{uuid\}.
         */
        name?: string | null;
        /**
         * Output only. ID of the `Rollout` phase this `JobRun` belongs in.
         */
        phaseId?: string | null;
        /**
         * Output only. Information specific to a postdeploy `JobRun`.
         */
        postdeployJobRun?: Schema$PostdeployJobRun;
        /**
         * Output only. Information specific to a predeploy `JobRun`.
         */
        predeployJobRun?: Schema$PredeployJobRun;
        /**
         * Output only. Time at which the `JobRun` was started.
         */
        startTime?: string | null;
        /**
         * Output only. The current state of the `JobRun`.
         */
        state?: string | null;
        /**
         * Output only. Unique identifier of the `JobRun`.
         */
        uid?: string | null;
        /**
         * Output only. Information specific to a verify `JobRun`.
         */
        verifyJobRun?: Schema$VerifyJobRun;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/jobrun_notification" Platform Log event that describes the failure to send JobRun resource update Pub/Sub notification.
     */
    export interface Schema$JobRunNotificationEvent {
        /**
         * The name of the `JobRun`.
         */
        jobRun?: string | null;
        /**
         * Debug message for when a notification fails to send.
         */
        message?: string | null;
        /**
         * Unique identifier of the `DeliveryPipeline`.
         */
        pipelineUid?: string | null;
        /**
         * Unique identifier of the `Release`.
         */
        releaseUid?: string | null;
        /**
         * Unique identifier of the `Rollout`.
         */
        rolloutUid?: string | null;
        /**
         * ID of the `Target`.
         */
        targetId?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * KubernetesConfig contains the Kubernetes runtime configuration.
     */
    export interface Schema$KubernetesConfig {
        /**
         * Kubernetes Gateway API service mesh configuration.
         */
        gatewayServiceMesh?: Schema$GatewayServiceMesh;
        /**
         * Kubernetes Service networking configuration.
         */
        serviceNetworking?: Schema$ServiceNetworking;
    }
    /**
     * The response object from `ListDeliveryPipelines`.
     */
    export interface Schema$ListDeliveryPipelinesResponse {
        /**
         * The `DeliveryPipeline` objects.
         */
        deliveryPipelines?: Schema$DeliveryPipeline[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * ListJobRunsResponse is the response object returned by `ListJobRuns`.
     */
    export interface Schema$ListJobRunsResponse {
        /**
         * The `JobRun` objects.
         */
        jobRuns?: Schema$JobRun[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * The response object from `ListReleases`.
     */
    export interface Schema$ListReleasesResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The `Release` objects.
         */
        releases?: Schema$Release[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * ListRolloutsResponse is the response object reutrned by `ListRollouts`.
     */
    export interface Schema$ListRolloutsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The `Rollout` objects.
         */
        rollouts?: Schema$Rollout[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response object from `ListTargets`.
     */
    export interface Schema$ListTargetsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The `Target` objects.
         */
        targets?: Schema$Target[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Metadata includes information associated with a `Rollout`.
     */
    export interface Schema$Metadata {
        /**
         * Output only. The name of the Cloud Run Service that is associated with a `Rollout`.
         */
        cloudRun?: Schema$CloudRunMetadata;
    }
    /**
     * Information specifying a multiTarget.
     */
    export interface Schema$MultiTarget {
        /**
         * Required. The target_ids of this multiTarget.
         */
        targetIds?: string[] | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Phase represents a collection of jobs that are logically grouped together for a `Rollout`.
     */
    export interface Schema$Phase {
        /**
         * Output only. ChildRollout job composition.
         */
        childRolloutJobs?: Schema$ChildRolloutJobs;
        /**
         * Output only. Deployment job composition.
         */
        deploymentJobs?: Schema$DeploymentJobs;
        /**
         * Output only. The ID of the Phase.
         */
        id?: string | null;
        /**
         * Output only. Additional information on why the Phase was skipped, if available.
         */
        skipMessage?: string | null;
        /**
         * Output only. Current state of the Phase.
         */
        state?: string | null;
    }
    /**
     * Contains the paths to the artifacts, relative to the URI, for a phase.
     */
    export interface Schema$PhaseArtifact {
        /**
         * Output only. File path of the directory of rendered job manifests relative to the URI. This is only set if it is applicable.
         */
        jobManifestsPath?: string | null;
        /**
         * Output only. File path of the rendered manifest relative to the URI.
         */
        manifestPath?: string | null;
        /**
         * Output only. File path of the resolved Skaffold configuration relative to the URI.
         */
        skaffoldConfigPath?: string | null;
    }
    /**
     * PhaseConfig represents the configuration for a phase in the custom canary deployment.
     */
    export interface Schema$PhaseConfig {
        /**
         * Required. Percentage deployment for the phase.
         */
        percentage?: number | null;
        /**
         * Required. The ID to assign to the `Rollout` phase. This value must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$`.
         */
        phaseId?: string | null;
        /**
         * Optional. Configuration for the postdeploy job of this phase. If this is not configured, there will be no postdeploy job for this phase.
         */
        postdeploy?: Schema$Postdeploy;
        /**
         * Optional. Configuration for the predeploy job of this phase. If this is not configured, there will be no predeploy job for this phase.
         */
        predeploy?: Schema$Predeploy;
        /**
         * Skaffold profiles to use when rendering the manifest for this phase. These are in addition to the profiles list specified in the `DeliveryPipeline` stage.
         */
        profiles?: string[] | null;
        /**
         * Whether to run verify tests after the deployment.
         */
        verify?: boolean | null;
    }
    /**
     * PipelineCondition contains all conditions relevant to a Delivery Pipeline.
     */
    export interface Schema$PipelineCondition {
        /**
         * Details around the Pipeline's overall status.
         */
        pipelineReadyCondition?: Schema$PipelineReadyCondition;
        /**
         * Details around targets enumerated in the pipeline.
         */
        targetsPresentCondition?: Schema$TargetsPresentCondition;
        /**
         * Details on the whether the targets enumerated in the pipeline are of the same type.
         */
        targetsTypeCondition?: Schema$TargetsTypeCondition;
    }
    /**
     * PipelineReadyCondition contains information around the status of the Pipeline.
     */
    export interface Schema$PipelineReadyCondition {
        /**
         * True if the Pipeline is in a valid state. Otherwise at least one condition in `PipelineCondition` is in an invalid state. Iterate over those conditions and see which condition(s) has status = false to find out what is wrong with the Pipeline.
         */
        status?: boolean | null;
        /**
         * Last time the condition was updated.
         */
        updateTime?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Postdeploy contains the postdeploy job configuration information.
     */
    export interface Schema$Postdeploy {
        /**
         * Optional. A sequence of Skaffold custom actions to invoke during execution of the postdeploy job.
         */
        actions?: string[] | null;
    }
    /**
     * A postdeploy Job.
     */
    export interface Schema$PostdeployJob {
        /**
         * Output only. The custom actions that the postdeploy Job executes.
         */
        actions?: string[] | null;
    }
    /**
     * PostdeployJobRun contains information specific to a postdeploy `JobRun`.
     */
    export interface Schema$PostdeployJobRun {
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to execute the custom actions associated with the postdeploy Job. Format is projects/{project\}/locations/{location\}/builds/{build\}.
         */
        build?: string | null;
        /**
         * Output only. The reason the postdeploy failed. This will always be unspecified while the postdeploy is in progress or if it succeeded.
         */
        failureCause?: string | null;
        /**
         * Output only. Additional information about the postdeploy failure, if available.
         */
        failureMessage?: string | null;
    }
    /**
     * Predeploy contains the predeploy job configuration information.
     */
    export interface Schema$Predeploy {
        /**
         * Optional. A sequence of Skaffold custom actions to invoke during execution of the predeploy job.
         */
        actions?: string[] | null;
    }
    /**
     * A predeploy Job.
     */
    export interface Schema$PredeployJob {
        /**
         * Output only. The custom actions that the predeploy Job executes.
         */
        actions?: string[] | null;
    }
    /**
     * PredeployJobRun contains information specific to a predeploy `JobRun`.
     */
    export interface Schema$PredeployJobRun {
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to execute the custom actions associated with the predeploy Job. Format is projects/{project\}/locations/{location\}/builds/{build\}.
         */
        build?: string | null;
        /**
         * Output only. The reason the predeploy failed. This will always be unspecified while the predeploy is in progress or if it succeeded.
         */
        failureCause?: string | null;
        /**
         * Output only. Additional information about the predeploy failure, if available.
         */
        failureMessage?: string | null;
    }
    /**
     * Execution using a private Cloud Build pool.
     */
    export interface Schema$PrivatePool {
        /**
         * Optional. Cloud Storage location where execution outputs should be stored. This can either be a bucket ("gs://my-bucket") or a path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a default bucket located in the same region will be used.
         */
        artifactStorage?: string | null;
        /**
         * Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) will be used.
         */
        serviceAccount?: string | null;
        /**
         * Required. Resource name of the Cloud Build worker pool to use. The format is `projects/{project\}/locations/{location\}/workerPools/{pool\}`.
         */
        workerPool?: string | null;
    }
    /**
     * A `Release` resource in the Cloud Deploy API. A `Release` defines a specific Skaffold configuration instance that can be deployed.
     */
    export interface Schema$Release {
        /**
         * Output only. Indicates whether this is an abandoned release.
         */
        abandoned?: boolean | null;
        /**
         * User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * List of artifacts to pass through to Skaffold command.
         */
        buildArtifacts?: Schema$BuildArtifact[];
        /**
         * Output only. Information around the state of the Release.
         */
        condition?: Schema$ReleaseCondition;
        /**
         * Output only. Time at which the `Release` was created.
         */
        createTime?: string | null;
        /**
         * Output only. Snapshot of the parent pipeline taken at release creation time.
         */
        deliveryPipelineSnapshot?: Schema$DeliveryPipeline;
        /**
         * Optional. The deploy parameters to use for all targets in this release.
         */
        deployParameters?: {
            [key: string]: string;
        } | null;
        /**
         * Description of the `Release`. Max length is 255 characters.
         */
        description?: string | null;
        /**
         * This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Name of the `Release`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/a-z{0,62\}.
         */
        name?: string | null;
        /**
         * Output only. Time at which the render completed.
         */
        renderEndTime?: string | null;
        /**
         * Output only. Time at which the render began.
         */
        renderStartTime?: string | null;
        /**
         * Output only. Current state of the render operation.
         */
        renderState?: string | null;
        /**
         * Filepath of the Skaffold config inside of the config URI.
         */
        skaffoldConfigPath?: string | null;
        /**
         * Cloud Storage URI of tar.gz archive containing Skaffold configuration.
         */
        skaffoldConfigUri?: string | null;
        /**
         * The Skaffold version to use when operating on this release, such as "1.20.0". Not all versions are valid; Cloud Deploy supports a specific set of versions. If unset, the most recent supported Skaffold version will be used.
         */
        skaffoldVersion?: string | null;
        /**
         * Output only. Map from target ID to the target artifacts created during the render operation.
         */
        targetArtifacts?: {
            [key: string]: Schema$TargetArtifact;
        } | null;
        /**
         * Output only. Map from target ID to details of the render operation for that target.
         */
        targetRenders?: {
            [key: string]: Schema$TargetRender;
        } | null;
        /**
         * Output only. Snapshot of the targets taken at release creation time.
         */
        targetSnapshots?: Schema$Target[];
        /**
         * Output only. Unique identifier of the `Release`.
         */
        uid?: string | null;
    }
    /**
     * ReleaseCondition contains all conditions relevant to a Release.
     */
    export interface Schema$ReleaseCondition {
        /**
         * Details around the Releases's overall status.
         */
        releaseReadyCondition?: Schema$ReleaseReadyCondition;
        /**
         * Details around the support state of the release's skaffold version.
         */
        skaffoldSupportedCondition?: Schema$SkaffoldSupportedCondition;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/release_notification" Platform Log event that describes the failure to send release status change Pub/Sub notification.
     */
    export interface Schema$ReleaseNotificationEvent {
        /**
         * Debug message for when a notification fails to send.
         */
        message?: string | null;
        /**
         * The name of the `Release`.
         */
        release?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * ReleaseReadyCondition contains information around the status of the Release. If a release is not ready, you cannot create a rollout with the release.
     */
    export interface Schema$ReleaseReadyCondition {
        /**
         * True if the Release is in a valid state. Otherwise at least one condition in `ReleaseCondition` is in an invalid state. Iterate over those conditions and see which condition(s) has status = false to find out what is wrong with the Release.
         */
        status?: boolean | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/release_render" Platform Log event that describes the render status change.
     */
    export interface Schema$ReleaseRenderEvent {
        /**
         * Debug message for when a render transition occurs. Provides further details as rendering progresses through render states.
         */
        message?: string | null;
        /**
         * The name of the `Release`.
         */
        release?: string | null;
    }
    /**
     * RenderMetadata includes information associated with a `Release` render.
     */
    export interface Schema$RenderMetadata {
        /**
         * Output only. Metadata associated with rendering for Cloud Run.
         */
        cloudRun?: Schema$CloudRunRenderMetadata;
    }
    /**
     * RetryJobRequest is the request object used by `RetryJob`.
     */
    export interface Schema$RetryJobRequest {
        /**
         * Required. The job ID for the Job to retry.
         */
        jobId?: string | null;
        /**
         * Required. The phase ID the Job to retry belongs to.
         */
        phaseId?: string | null;
    }
    /**
     * The response object from 'RetryJob'.
     */
    export interface Schema$RetryJobResponse {
    }
    /**
     * Configs for the Rollback rollout.
     */
    export interface Schema$RollbackTargetConfig {
        /**
         * Optional. The rollback `Rollout` to create.
         */
        rollout?: Schema$Rollout;
        /**
         * Optional. The starting phase ID for the `Rollout`. If unspecified, the `Rollout` will start in the stable phase.
         */
        startingPhaseId?: string | null;
    }
    /**
     * The request object for `RollbackTarget`.
     */
    export interface Schema$RollbackTargetRequest {
        /**
         * Optional. ID of the `Release` to roll back to. If this isn't specified, the previous successful `Rollout` to the specified target will be used to determine the `Release`.
         */
        releaseId?: string | null;
        /**
         * Optional. Configs for the rollback `Rollout`.
         */
        rollbackConfig?: Schema$RollbackTargetConfig;
        /**
         * Required. ID of the rollback `Rollout` to create.
         */
        rolloutId?: string | null;
        /**
         * Optional. If provided, this must be the latest `Rollout` that is on the `Target`.
         */
        rolloutToRollBack?: string | null;
        /**
         * Required. ID of the `Target` that is being rolled back.
         */
        targetId?: string | null;
        /**
         * Optional. If set to true, the request is validated and the user is provided with a `RollbackTargetResponse`.
         */
        validateOnly?: boolean | null;
    }
    /**
     * The response object from `RollbackTarget`.
     */
    export interface Schema$RollbackTargetResponse {
        /**
         * The config of the rollback `Rollout` created or will be created.
         */
        rollbackConfig?: Schema$RollbackTargetConfig;
    }
    /**
     * A `Rollout` resource in the Cloud Deploy API. A `Rollout` contains information around a specific deployment to a `Target`.
     */
    export interface Schema$Rollout {
        /**
         * User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Approval state of the `Rollout`.
         */
        approvalState?: string | null;
        /**
         * Output only. Time at which the `Rollout` was approved.
         */
        approveTime?: string | null;
        /**
         * Output only. Name of the `ControllerRollout`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/a-z{0,62\}.
         */
        controllerRollout?: string | null;
        /**
         * Output only. Time at which the `Rollout` was created.
         */
        createTime?: string | null;
        /**
         * Output only. Time at which the `Rollout` finished deploying.
         */
        deployEndTime?: string | null;
        /**
         * Output only. The reason this rollout failed. This will always be unspecified while the rollout is in progress.
         */
        deployFailureCause?: string | null;
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to deploy the Rollout. Format is `projects/{project\}/locations/{location\}/builds/{build\}`.
         */
        deployingBuild?: string | null;
        /**
         * Output only. Time at which the `Rollout` started deploying.
         */
        deployStartTime?: string | null;
        /**
         * Description of the `Rollout` for user purposes. Max length is 255 characters.
         */
        description?: string | null;
        /**
         * Output only. Time at which the `Rollout` was enqueued.
         */
        enqueueTime?: string | null;
        /**
         * This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Output only. Additional information about the rollout failure, if available.
         */
        failureReason?: string | null;
        /**
         * Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Metadata contains information about the rollout.
         */
        metadata?: Schema$Metadata;
        /**
         * Optional. Name of the `Rollout`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/a-z{0,62\}.
         */
        name?: string | null;
        /**
         * Output only. The phases that represent the workflows of this `Rollout`.
         */
        phases?: Schema$Phase[];
        /**
         * Output only. Name of the `Rollout` that is rolled back by this `Rollout`. Empty if this `Rollout` wasn't created as a rollback.
         */
        rollbackOfRollout?: string | null;
        /**
         * Output only. Names of `Rollouts` that rolled back this `Rollout`.
         */
        rolledBackByRollouts?: string[] | null;
        /**
         * Output only. Current state of the `Rollout`.
         */
        state?: string | null;
        /**
         * Required. The ID of Target to which this `Rollout` is deploying.
         */
        targetId?: string | null;
        /**
         * Output only. Unique identifier of the `Rollout`.
         */
        uid?: string | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/rollout_notification" Platform Log event that describes the failure to send rollout status change Pub/Sub notification.
     */
    export interface Schema$RolloutNotificationEvent {
        /**
         * Debug message for when a notification fails to send.
         */
        message?: string | null;
        /**
         * Unique identifier of the `DeliveryPipeline`.
         */
        pipelineUid?: string | null;
        /**
         * Unique identifier of the `Release`.
         */
        releaseUid?: string | null;
        /**
         * The name of the `Rollout`.
         */
        rollout?: string | null;
        /**
         * ID of the `Target` that the rollout is deployed to.
         */
        targetId?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * RuntimeConfig contains the runtime specific configurations for a deployment strategy.
     */
    export interface Schema$RuntimeConfig {
        /**
         * Cloud Run runtime configuration.
         */
        cloudRun?: Schema$CloudRunConfig;
        /**
         * Kubernetes runtime configuration.
         */
        kubernetes?: Schema$KubernetesConfig;
    }
    /**
     * SerialPipeline defines a sequential set of stages for a `DeliveryPipeline`.
     */
    export interface Schema$SerialPipeline {
        /**
         * Each stage specifies configuration for a `Target`. The ordering of this list defines the promotion flow.
         */
        stages?: Schema$Stage[];
    }
    /**
     * Information about the Kubernetes Service networking configuration.
     */
    export interface Schema$ServiceNetworking {
        /**
         * Required. Name of the Kubernetes Deployment whose traffic is managed by the specified Service.
         */
        deployment?: string | null;
        /**
         * Optional. Whether to disable Pod overprovisioning. If Pod overprovisioning is disabled then Cloud Deploy will limit the number of total Pods used for the deployment strategy to the number of Pods the Deployment has on the cluster.
         */
        disablePodOverprovisioning?: boolean | null;
        /**
         * Required. Name of the Kubernetes Service.
         */
        service?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * SkaffoldSupportedCondition contains information about when support for the release's version of skaffold ends.
     */
    export interface Schema$SkaffoldSupportedCondition {
        /**
         * The time at which this release's version of skaffold will enter maintenance mode.
         */
        maintenanceModeTime?: string | null;
        /**
         * The skaffold support state for this release's version of skaffold.
         */
        skaffoldSupportState?: string | null;
        /**
         * True if the version of skaffold used by this release is supported.
         */
        status?: boolean | null;
        /**
         * The time at which this release's version of skaffold will no longer be supported.
         */
        supportExpirationTime?: string | null;
    }
    /**
     * Details of a supported Skaffold version.
     */
    export interface Schema$SkaffoldVersion {
        /**
         * The time at which this version of skaffold will enter maintenance mode.
         */
        maintenanceModeTime?: string | null;
        /**
         * Date when this version is expected to no longer be supported.
         */
        supportEndDate?: Schema$Date;
        /**
         * The time at which this version of skaffold will no longer be supported.
         */
        supportExpirationTime?: string | null;
        /**
         * Release version number. For example, "1.20.3".
         */
        version?: string | null;
    }
    /**
     * Stage specifies a location to which to deploy.
     */
    export interface Schema$Stage {
        /**
         * Optional. The deploy parameters to use for the target in this stage.
         */
        deployParameters?: Schema$DeployParameters[];
        /**
         * Skaffold profiles to use when rendering the manifest for this stage's `Target`.
         */
        profiles?: string[] | null;
        /**
         * Optional. The strategy to use for a `Rollout` to this stage.
         */
        strategy?: Schema$Strategy;
        /**
         * The target_id to which this stage points. This field refers exclusively to the last segment of a target name. For example, this field would just be `my-target` (rather than `projects/project/locations/location/targets/my-target`). The location of the `Target` is inferred to be the same as the location of the `DeliveryPipeline` that contains this `Stage`.
         */
        targetId?: string | null;
    }
    /**
     * Standard represents the standard deployment strategy.
     */
    export interface Schema$Standard {
        /**
         * Optional. Configuration for the postdeploy job. If this is not configured, postdeploy job will not be present.
         */
        postdeploy?: Schema$Postdeploy;
        /**
         * Optional. Configuration for the predeploy job. If this is not configured, predeploy job will not be present.
         */
        predeploy?: Schema$Predeploy;
        /**
         * Whether to verify a deployment.
         */
        verify?: boolean | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Strategy contains deployment strategy information.
     */
    export interface Schema$Strategy {
        /**
         * Canary deployment strategy provides progressive percentage based deployments to a Target.
         */
        canary?: Schema$Canary;
        /**
         * Standard deployment strategy executes a single deploy and allows verifying the deployment.
         */
        standard?: Schema$Standard;
    }
    /**
     * A `Target` resource in the Cloud Deploy API. A `Target` defines a location to which a Skaffold configuration can be deployed.
     */
    export interface Schema$Target {
        /**
         * Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Information specifying an Anthos Cluster.
         */
        anthosCluster?: Schema$AnthosCluster;
        /**
         * Output only. Time at which the `Target` was created.
         */
        createTime?: string | null;
        /**
         * Optional. The deploy parameters to use for this target.
         */
        deployParameters?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Description of the `Target`. Max length is 255 characters.
         */
        description?: string | null;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Configurations for all execution that relates to this `Target`. Each `ExecutionEnvironmentUsage` value may only be used in a single configuration; using the same value multiple times is an error. When one or more configurations are specified, they must include the `RENDER` and `DEPLOY` `ExecutionEnvironmentUsage` values. When no configurations are specified, execution will use the default specified in `DefaultPool`.
         */
        executionConfigs?: Schema$ExecutionConfig[];
        /**
         * Optional. Information specifying a GKE Cluster.
         */
        gke?: Schema$GkeCluster;
        /**
         * Optional. Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Information specifying a multiTarget.
         */
        multiTarget?: Schema$MultiTarget;
        /**
         * Optional. Name of the `Target`. Format is projects/{project\}/locations/{location\}/targets/a-z{0,62\}.
         */
        name?: string | null;
        /**
         * Optional. Whether or not the `Target` requires approval.
         */
        requireApproval?: boolean | null;
        /**
         * Optional. Information specifying a Cloud Run deployment target.
         */
        run?: Schema$CloudRunLocation;
        /**
         * Output only. Resource id of the `Target`.
         */
        targetId?: string | null;
        /**
         * Output only. Unique identifier of the `Target`.
         */
        uid?: string | null;
        /**
         * Output only. Most recent time at which the `Target` was updated.
         */
        updateTime?: string | null;
    }
    /**
     * The artifacts produced by a target render operation.
     */
    export interface Schema$TargetArtifact {
        /**
         * Output only. URI of a directory containing the artifacts. This contains deployment configuration used by Skaffold during a rollout, and all paths are relative to this location.
         */
        artifactUri?: string | null;
        /**
         * Output only. File path of the rendered manifest relative to the URI.
         */
        manifestPath?: string | null;
        /**
         * Output only. Map from the phase ID to the phase artifacts for the `Target`.
         */
        phaseArtifacts?: {
            [key: string]: Schema$PhaseArtifact;
        } | null;
        /**
         * Output only. File path of the resolved Skaffold configuration relative to the URI.
         */
        skaffoldConfigPath?: string | null;
    }
    /**
     * Payload proto for "clouddeploy.googleapis.com/target_notification" Platform Log event that describes the failure to send target status change Pub/Sub notification.
     */
    export interface Schema$TargetNotificationEvent {
        /**
         * Debug message for when a notification fails to send.
         */
        message?: string | null;
        /**
         * The name of the `Target`.
         */
        target?: string | null;
        /**
         * Type of this notification, e.g. for a Pub/Sub failure.
         */
        type?: string | null;
    }
    /**
     * Details of rendering for a single target.
     */
    export interface Schema$TargetRender {
        /**
         * Output only. Reason this render failed. This will always be unspecified while the render in progress.
         */
        failureCause?: string | null;
        /**
         * Output only. Additional information about the render failure, if available.
         */
        failureMessage?: string | null;
        /**
         * Output only. Metadata related to the `Release` render for this Target.
         */
        metadata?: Schema$RenderMetadata;
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to render the manifest for this target. Format is `projects/{project\}/locations/{location\}/builds/{build\}`.
         */
        renderingBuild?: string | null;
        /**
         * Output only. Current state of the render operation for this Target.
         */
        renderingState?: string | null;
    }
    /**
     * TargetsPresentCondition contains information on any Targets defined in the Delivery Pipeline that do not actually exist.
     */
    export interface Schema$TargetsPresentCondition {
        /**
         * The list of Target names that do not exist. For example, projects/{project_id\}/locations/{location_name\}/targets/{target_name\}.
         */
        missingTargets?: string[] | null;
        /**
         * True if there aren't any missing Targets.
         */
        status?: boolean | null;
        /**
         * Last time the condition was updated.
         */
        updateTime?: string | null;
    }
    /**
     * TargetsTypeCondition contains information on whether the Targets defined in the Delivery Pipeline are of the same type.
     */
    export interface Schema$TargetsTypeCondition {
        /**
         * Human readable error message.
         */
        errorDetails?: string | null;
        /**
         * True if the targets are all a comparable type. For example this is true if all targets are GKE clusters. This is false if some targets are Cloud Run targets and others are GKE clusters.
         */
        status?: boolean | null;
    }
    /**
     * The request object used by `TerminateJobRun`.
     */
    export interface Schema$TerminateJobRunRequest {
    }
    /**
     * The response object from `TerminateJobRun`.
     */
    export interface Schema$TerminateJobRunResponse {
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A verify Job.
     */
    export interface Schema$VerifyJob {
    }
    /**
     * VerifyJobRun contains information specific to a verify `JobRun`.
     */
    export interface Schema$VerifyJobRun {
        /**
         * Output only. URI of a directory containing the verify artifacts. This contains the Skaffold event log.
         */
        artifactUri?: string | null;
        /**
         * Output only. The resource name of the Cloud Build `Build` object that is used to verify. Format is projects/{project\}/locations/{location\}/builds/{build\}.
         */
        build?: string | null;
        /**
         * Output only. File path of the Skaffold event log relative to the artifact URI.
         */
        eventLogPath?: string | null;
        /**
         * Output only. The reason the verify failed. This will always be unspecified while the verify is in progress or if it succeeded.
         */
        failureCause?: string | null;
        /**
         * Output only. Additional information about the verify failure, if available.
         */
        failureMessage?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        deliveryPipelines: Resource$Projects$Locations$Deliverypipelines;
        operations: Resource$Projects$Locations$Operations;
        targets: Resource$Projects$Locations$Targets;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Gets the configuration for a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getConfig(params: Params$Resource$Projects$Locations$Getconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getConfig(params?: Params$Resource$Projects$Locations$Getconfig, options?: MethodOptions): GaxiosPromise<Schema$Config>;
        getConfig(params: Params$Resource$Projects$Locations$Getconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getConfig(params: Params$Resource$Projects$Locations$Getconfig, options: MethodOptions | BodyResponseCallback<Schema$Config>, callback: BodyResponseCallback<Schema$Config>): void;
        getConfig(params: Params$Resource$Projects$Locations$Getconfig, callback: BodyResponseCallback<Schema$Config>): void;
        getConfig(callback: BodyResponseCallback<Schema$Config>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Getconfig extends StandardParameters {
        /**
         * Required. Name of requested configuration.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Deliverypipelines {
        context: APIRequestContext;
        releases: Resource$Projects$Locations$Deliverypipelines$Releases;
        constructor(context: APIRequestContext);
        /**
         * Creates a new DeliveryPipeline in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Deliverypipelines$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single DeliveryPipeline.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Deliverypipelines$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Deliverypipelines$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Deliverypipelines$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Deliverypipelines$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Deliverypipelines$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single DeliveryPipeline.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Deliverypipelines$Get, options?: MethodOptions): GaxiosPromise<Schema$DeliveryPipeline>;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Get, options: MethodOptions | BodyResponseCallback<Schema$DeliveryPipeline>, callback: BodyResponseCallback<Schema$DeliveryPipeline>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Get, callback: BodyResponseCallback<Schema$DeliveryPipeline>): void;
        get(callback: BodyResponseCallback<Schema$DeliveryPipeline>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists DeliveryPipelines in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Deliverypipelines$List, options?: MethodOptions): GaxiosPromise<Schema$ListDeliveryPipelinesResponse>;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$List, options: MethodOptions | BodyResponseCallback<Schema$ListDeliveryPipelinesResponse>, callback: BodyResponseCallback<Schema$ListDeliveryPipelinesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$List, callback: BodyResponseCallback<Schema$ListDeliveryPipelinesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDeliveryPipelinesResponse>): void;
        /**
         * Updates the parameters of a single DeliveryPipeline.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Deliverypipelines$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Deliverypipelines$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Deliverypipelines$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Deliverypipelines$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Deliverypipelines$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a `Rollout` to roll back the specified target.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        rollbackTarget(params: Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget, options: StreamMethodOptions): GaxiosPromise<Readable>;
        rollbackTarget(params?: Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget, options?: MethodOptions): GaxiosPromise<Schema$RollbackTargetResponse>;
        rollbackTarget(params: Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        rollbackTarget(params: Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget, options: MethodOptions | BodyResponseCallback<Schema$RollbackTargetResponse>, callback: BodyResponseCallback<Schema$RollbackTargetResponse>): void;
        rollbackTarget(params: Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget, callback: BodyResponseCallback<Schema$RollbackTargetResponse>): void;
        rollbackTarget(callback: BodyResponseCallback<Schema$RollbackTargetResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Create extends StandardParameters {
        /**
         * Required. ID of the `DeliveryPipeline`.
         */
        deliveryPipelineId?: string;
        /**
         * Required. The parent collection in which the `DeliveryPipeline` should be created. Format should be projects/{project_id\}/locations/{location_name\}.
         */
        parent?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeliveryPipeline;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Delete extends StandardParameters {
        /**
         * Optional. If set to true, then deleting an already deleted or non-existing `DeliveryPipeline` will succeed.
         */
        allowMissing?: boolean;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string;
        /**
         * Optional. If set to true, all child resources under this pipeline will also be deleted. Otherwise, the request will only work if the pipeline has no child resources.
         */
        force?: boolean;
        /**
         * Required. The name of the `DeliveryPipeline` to delete. Format should be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}.
         */
        name?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validate the request and preview the review, but do not actually post it.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Get extends StandardParameters {
        /**
         * Required. Name of the `DeliveryPipeline`. Format must be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$List extends StandardParameters {
        /**
         * Filter pipelines to be returned. See https://google.aip.dev/160 for more details.
         */
        filter?: string;
        /**
         * Field to sort by. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * The maximum number of pipelines to return. The service may return fewer than this value. If unspecified, at most 50 pipelines will be returned. The maximum value is 1000; values above 1000 will be set to 1000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListDeliveryPipelines` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of pipelines. Format must be projects/{project_id\}/locations/{location_name\}.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Patch extends StandardParameters {
        /**
         * Optional. If set to true, updating a `DeliveryPipeline` that does not exist will result in the creation of a new `DeliveryPipeline`.
         */
        allowMissing?: boolean;
        /**
         * Optional. Name of the `DeliveryPipeline`. Format is projects/{project\}/ locations/{location\}/deliveryPipelines/a-z{0,62\}.
         */
        name?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the `DeliveryPipeline` resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeliveryPipeline;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Rollbacktarget extends StandardParameters {
        /**
         * Required. The `DeliveryPipeline` for which the rollback `Rollout` should be created. Format should be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RollbackTargetRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Deliverypipelines$Releases {
        context: APIRequestContext;
        rollouts: Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts;
        constructor(context: APIRequestContext);
        /**
         * Abandons a Release in the Delivery Pipeline.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        abandon(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon, options: StreamMethodOptions): GaxiosPromise<Readable>;
        abandon(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon, options?: MethodOptions): GaxiosPromise<Schema$AbandonReleaseResponse>;
        abandon(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        abandon(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon, options: MethodOptions | BodyResponseCallback<Schema$AbandonReleaseResponse>, callback: BodyResponseCallback<Schema$AbandonReleaseResponse>): void;
        abandon(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon, callback: BodyResponseCallback<Schema$AbandonReleaseResponse>): void;
        abandon(callback: BodyResponseCallback<Schema$AbandonReleaseResponse>): void;
        /**
         * Creates a new Release in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get, options?: MethodOptions): GaxiosPromise<Schema$Release>;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get, options: MethodOptions | BodyResponseCallback<Schema$Release>, callback: BodyResponseCallback<Schema$Release>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get, callback: BodyResponseCallback<Schema$Release>): void;
        get(callback: BodyResponseCallback<Schema$Release>): void;
        /**
         * Lists Releases in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$List, options?: MethodOptions): GaxiosPromise<Schema$ListReleasesResponse>;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$List, options: MethodOptions | BodyResponseCallback<Schema$ListReleasesResponse>, callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$List, callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Abandon extends StandardParameters {
        /**
         * Required. Name of the Release. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AbandonReleaseRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Create extends StandardParameters {
        /**
         * Required. The parent collection in which the `Release` should be created. Format should be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}.
         */
        parent?: string;
        /**
         * Required. ID of the `Release`.
         */
        releaseId?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Release;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Get extends StandardParameters {
        /**
         * Required. Name of the `Release`. Format must be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}/releases/{release_name\}.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$List extends StandardParameters {
        /**
         * Optional. Filter releases to be returned. See https://google.aip.dev/160 for more details.
         */
        filter?: string;
        /**
         * Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of `Release` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Release` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListReleases` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The `DeliveryPipeline` which owns this collection of `Release` objects.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts {
        context: APIRequestContext;
        jobRuns: Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns;
        constructor(context: APIRequestContext);
        /**
         * Advances a Rollout in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        advance(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance, options: StreamMethodOptions): GaxiosPromise<Readable>;
        advance(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance, options?: MethodOptions): GaxiosPromise<Schema$AdvanceRolloutResponse>;
        advance(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        advance(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance, options: MethodOptions | BodyResponseCallback<Schema$AdvanceRolloutResponse>, callback: BodyResponseCallback<Schema$AdvanceRolloutResponse>): void;
        advance(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance, callback: BodyResponseCallback<Schema$AdvanceRolloutResponse>): void;
        advance(callback: BodyResponseCallback<Schema$AdvanceRolloutResponse>): void;
        /**
         * Approves a Rollout.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        approve(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve, options: StreamMethodOptions): GaxiosPromise<Readable>;
        approve(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve, options?: MethodOptions): GaxiosPromise<Schema$ApproveRolloutResponse>;
        approve(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        approve(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve, options: MethodOptions | BodyResponseCallback<Schema$ApproveRolloutResponse>, callback: BodyResponseCallback<Schema$ApproveRolloutResponse>): void;
        approve(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve, callback: BodyResponseCallback<Schema$ApproveRolloutResponse>): void;
        approve(callback: BodyResponseCallback<Schema$ApproveRolloutResponse>): void;
        /**
         * Cancels a Rollout in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel, options?: MethodOptions): GaxiosPromise<Schema$CancelRolloutResponse>;
        cancel(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel, options: MethodOptions | BodyResponseCallback<Schema$CancelRolloutResponse>, callback: BodyResponseCallback<Schema$CancelRolloutResponse>): void;
        cancel(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel, callback: BodyResponseCallback<Schema$CancelRolloutResponse>): void;
        cancel(callback: BodyResponseCallback<Schema$CancelRolloutResponse>): void;
        /**
         * Creates a new Rollout in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Rollout.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get, options?: MethodOptions): GaxiosPromise<Schema$Rollout>;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get, options: MethodOptions | BodyResponseCallback<Schema$Rollout>, callback: BodyResponseCallback<Schema$Rollout>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get, callback: BodyResponseCallback<Schema$Rollout>): void;
        get(callback: BodyResponseCallback<Schema$Rollout>): void;
        /**
         * Ignores the specified Job in a Rollout.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        ignoreJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob, options: StreamMethodOptions): GaxiosPromise<Readable>;
        ignoreJob(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob, options?: MethodOptions): GaxiosPromise<Schema$IgnoreJobResponse>;
        ignoreJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        ignoreJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob, options: MethodOptions | BodyResponseCallback<Schema$IgnoreJobResponse>, callback: BodyResponseCallback<Schema$IgnoreJobResponse>): void;
        ignoreJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob, callback: BodyResponseCallback<Schema$IgnoreJobResponse>): void;
        ignoreJob(callback: BodyResponseCallback<Schema$IgnoreJobResponse>): void;
        /**
         * Lists Rollouts in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List, options?: MethodOptions): GaxiosPromise<Schema$ListRolloutsResponse>;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List, options: MethodOptions | BodyResponseCallback<Schema$ListRolloutsResponse>, callback: BodyResponseCallback<Schema$ListRolloutsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List, callback: BodyResponseCallback<Schema$ListRolloutsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRolloutsResponse>): void;
        /**
         * Retries the specified Job in a Rollout.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        retryJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob, options: StreamMethodOptions): GaxiosPromise<Readable>;
        retryJob(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob, options?: MethodOptions): GaxiosPromise<Schema$RetryJobResponse>;
        retryJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        retryJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob, options: MethodOptions | BodyResponseCallback<Schema$RetryJobResponse>, callback: BodyResponseCallback<Schema$RetryJobResponse>): void;
        retryJob(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob, callback: BodyResponseCallback<Schema$RetryJobResponse>): void;
        retryJob(callback: BodyResponseCallback<Schema$RetryJobResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Advance extends StandardParameters {
        /**
         * Required. Name of the Rollout. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AdvanceRolloutRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Approve extends StandardParameters {
        /**
         * Required. Name of the Rollout. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApproveRolloutRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Cancel extends StandardParameters {
        /**
         * Required. Name of the Rollout. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelRolloutRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Create extends StandardParameters {
        /**
         * Required. The parent collection in which the `Rollout` should be created. Format should be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}/releases/{release_name\}.
         */
        parent?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. ID of the `Rollout`.
         */
        rolloutId?: string;
        /**
         * Optional. The starting phase ID for the `Rollout`. If empty the `Rollout` will start at the first phase.
         */
        startingPhaseId?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Rollout;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Get extends StandardParameters {
        /**
         * Required. Name of the `Rollout`. Format must be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}/releases/{release_name\}/rollouts/{rollout_name\}.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Ignorejob extends StandardParameters {
        /**
         * Required. Name of the Rollout. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}.
         */
        rollout?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$IgnoreJobRequest;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$List extends StandardParameters {
        /**
         * Optional. Filter rollouts to be returned. See https://google.aip.dev/160 for more details.
         */
        filter?: string;
        /**
         * Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of `Rollout` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Rollout` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListRollouts` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The `Release` which owns this collection of `Rollout` objects.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Retryjob extends StandardParameters {
        /**
         * Required. Name of the Rollout. Format is projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}.
         */
        rollout?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RetryJobRequest;
    }
    export class Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets details of a single JobRun.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get, options?: MethodOptions): GaxiosPromise<Schema$JobRun>;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get, options: MethodOptions | BodyResponseCallback<Schema$JobRun>, callback: BodyResponseCallback<Schema$JobRun>): void;
        get(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get, callback: BodyResponseCallback<Schema$JobRun>): void;
        get(callback: BodyResponseCallback<Schema$JobRun>): void;
        /**
         * Lists JobRuns in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List, options?: MethodOptions): GaxiosPromise<Schema$ListJobRunsResponse>;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List, options: MethodOptions | BodyResponseCallback<Schema$ListJobRunsResponse>, callback: BodyResponseCallback<Schema$ListJobRunsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List, callback: BodyResponseCallback<Schema$ListJobRunsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListJobRunsResponse>): void;
        /**
         * Terminates a Job Run in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        terminate(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        terminate(params?: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate, options?: MethodOptions): GaxiosPromise<Schema$TerminateJobRunResponse>;
        terminate(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        terminate(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate, options: MethodOptions | BodyResponseCallback<Schema$TerminateJobRunResponse>, callback: BodyResponseCallback<Schema$TerminateJobRunResponse>): void;
        terminate(params: Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate, callback: BodyResponseCallback<Schema$TerminateJobRunResponse>): void;
        terminate(callback: BodyResponseCallback<Schema$TerminateJobRunResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Get extends StandardParameters {
        /**
         * Required. Name of the `JobRun`. Format must be projects/{project_id\}/locations/{location_name\}/deliveryPipelines/{pipeline_name\}/releases/{release_name\}/rollouts/{rollout_name\}/jobRuns/{job_run_name\}.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$List extends StandardParameters {
        /**
         * Optional. Filter results to be returned. See https://google.aip.dev/160 for more details.
         */
        filter?: string;
        /**
         * Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of `JobRun` objects to return. The service may return fewer than this value. If unspecified, at most 50 `JobRun` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListJobRuns` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The `Rollout` which owns this collection of `JobRun` objects.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Deliverypipelines$Releases$Rollouts$Jobruns$Terminate extends StandardParameters {
        /**
         * Required. Name of the `JobRun`. Format must be projects/{project\}/locations/{location\}/deliveryPipelines/{deliveryPipeline\}/ releases/{release\}/rollouts/{rollout\}/jobRuns/{jobRun\}.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TerminateJobRunRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Targets {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Target in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Targets$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Targets$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Targets$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Targets$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Targets$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Target.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Targets$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Targets$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Targets$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Targets$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Targets$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Target.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Targets$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Targets$Get, options?: MethodOptions): GaxiosPromise<Schema$Target>;
        get(params: Params$Resource$Projects$Locations$Targets$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Targets$Get, options: MethodOptions | BodyResponseCallback<Schema$Target>, callback: BodyResponseCallback<Schema$Target>): void;
        get(params: Params$Resource$Projects$Locations$Targets$Get, callback: BodyResponseCallback<Schema$Target>): void;
        get(callback: BodyResponseCallback<Schema$Target>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Targets$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Targets$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Targets$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Targets$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Targets$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Targets in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Targets$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Targets$List, options?: MethodOptions): GaxiosPromise<Schema$ListTargetsResponse>;
        list(params: Params$Resource$Projects$Locations$Targets$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Targets$List, options: MethodOptions | BodyResponseCallback<Schema$ListTargetsResponse>, callback: BodyResponseCallback<Schema$ListTargetsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Targets$List, callback: BodyResponseCallback<Schema$ListTargetsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTargetsResponse>): void;
        /**
         * Updates the parameters of a single Target.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Targets$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Targets$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Targets$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Targets$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Targets$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Targets$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Targets$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Targets$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Targets$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Targets$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Targets$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Targets$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Targets$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Targets$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Targets$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Targets$Create extends StandardParameters {
        /**
         * Required. The parent collection in which the `Target` should be created. Format should be projects/{project_id\}/locations/{location_name\}.
         */
        parent?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. ID of the `Target`.
         */
        targetId?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Target;
    }
    export interface Params$Resource$Projects$Locations$Targets$Delete extends StandardParameters {
        /**
         * Optional. If set to true, then deleting an already deleted or non-existing `Target` will succeed.
         */
        allowMissing?: boolean;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string;
        /**
         * Required. The name of the `Target` to delete. Format should be projects/{project_id\}/locations/{location_name\}/targets/{target_name\}.
         */
        name?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validate the request and preview the review, but do not actually post it.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Targets$Get extends StandardParameters {
        /**
         * Required. Name of the `Target`. Format must be projects/{project_id\}/locations/{location_name\}/targets/{target_name\}.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Targets$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Targets$List extends StandardParameters {
        /**
         * Optional. Filter targets to be returned. See https://google.aip.dev/160 for more details.
         */
        filter?: string;
        /**
         * Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of `Target` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Target` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListTargets` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of targets. Format must be projects/{project_id\}/locations/{location_name\}.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Targets$Patch extends StandardParameters {
        /**
         * Optional. If set to true, updating a `Target` that does not exist will result in the creation of a new `Target`.
         */
        allowMissing?: boolean;
        /**
         * Optional. Name of the `Target`. Format is projects/{project\}/locations/{location\}/targets/a-z{0,62\}.
         */
        name?: string;
        /**
         * Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the Target resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Target;
    }
    export interface Params$Resource$Projects$Locations$Targets$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Targets$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export {};
}
