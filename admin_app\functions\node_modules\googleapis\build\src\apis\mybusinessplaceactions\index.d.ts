/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessplaceactions_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessplaceactions_v1.Mybusinessplaceactions;
};
export declare function mybusinessplaceactions(version: 'v1'): mybusinessplaceactions_v1.Mybusinessplaceactions;
export declare function mybusinessplaceactions(options: mybusinessplaceactions_v1.Options): mybusinessplaceactions_v1.Mybusinessplaceactions;
declare const auth: AuthPlus;
export { auth };
export { mybusinessplaceactions_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
