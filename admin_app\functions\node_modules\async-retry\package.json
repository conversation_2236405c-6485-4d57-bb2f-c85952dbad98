{"name": "async-retry", "version": "1.3.3", "description": "Retrying made simple, easy and async", "main": "./lib/index.js", "scripts": {"test": "yarn run test-lint && yarn run test-unit", "test-lint": "eslint .", "test-unit": "ava", "lint:staged": "lint-staged"}, "files": ["lib"], "license": "MIT", "repository": "vercel/async-retry", "ava": {"failFast": true}, "dependencies": {"retry": "0.13.1"}, "pre-commit": "lint:staged", "lint-staged": {"*.js": ["eslint", "prettier --write --single-quote", "git add"]}, "eslintConfig": {"extends": ["airbnb", "prettier"], "rules": {"no-var": 0, "prefer-arrow-callback": 0}}, "devDependencies": {"ava": "3.15.0", "eslint": "7.32.0", "eslint-config-airbnb": "18.2.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.0", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-react": "7.24.0", "lint-staged": "11.1.2", "node-fetch": "2.6.1", "pre-commit": "1.2.2", "prettier": "2.3.2", "then-sleep": "1.0.1"}}