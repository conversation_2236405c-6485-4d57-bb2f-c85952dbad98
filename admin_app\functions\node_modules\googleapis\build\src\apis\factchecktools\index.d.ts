/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { factchecktools_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof factchecktools_v1alpha1.Factchecktools;
};
export declare function factchecktools(version: 'v1alpha1'): factchecktools_v1alpha1.Factchecktools;
export declare function factchecktools(options: factchecktools_v1alpha1.Options): factchecktools_v1alpha1.Factchecktools;
declare const auth: AuthPlus;
export { auth };
export { factchecktools_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
