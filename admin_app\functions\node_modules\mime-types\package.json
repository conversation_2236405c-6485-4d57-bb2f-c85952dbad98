{"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.35", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://searchbeam.jit.su)", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["mime", "types"], "repository": "jshttp/mime-types", "dependencies": {"mime-db": "1.52.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}