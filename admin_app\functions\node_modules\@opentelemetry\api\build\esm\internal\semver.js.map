{"version": 3, "file": "semver.js", "sourceRoot": "", "sources": ["../../../src/internal/semver.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,IAAM,EAAE,GAAG,+BAA+B,CAAC;AAE3C;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,uBAAuB,CACrC,UAAkB;IAElB,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAS,CAAC,UAAU,CAAC,CAAC,CAAC;IACvD,IAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE3C,IAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5C,IAAI,CAAC,cAAc,EAAE;QACnB,6DAA6D;QAC7D,OAAO,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC;KACpB;IAED,IAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC;KAC9B,CAAC;IAEF,kEAAkE;IAClE,IAAI,gBAAgB,CAAC,UAAU,IAAI,IAAI,EAAE;QACvC,OAAO,SAAS,YAAY,CAAC,aAAqB;YAChD,OAAO,aAAa,KAAK,UAAU,CAAC;QACtC,CAAC,CAAC;KACH;IAED,SAAS,OAAO,CAAC,CAAS;QACxB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,OAAO,CAAC,CAAS;QACxB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,SAAS,YAAY,CAAC,aAAqB;QAChD,IAAI,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,IAAM,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,kBAAkB,EAAE;YACvB,6BAA6B;YAC7B,sDAAsD;YACtD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAM,mBAAmB,GAAG;YAC1B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,UAAU,EAAE,kBAAkB,CAAC,CAAC,CAAC;SAClC,CAAC;QAEF,qEAAqE;QACrE,IAAI,mBAAmB,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1C,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,4BAA4B;QAC5B,IAAI,gBAAgB,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,EAAE;YACxD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAI,gBAAgB,CAAC,KAAK,KAAK,CAAC,EAAE;YAChC,IACE,gBAAgB,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK;gBACpD,gBAAgB,CAAC,KAAK,IAAI,mBAAmB,CAAC,KAAK,EACnD;gBACA,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;aAC/B;YAED,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAI,gBAAgB,CAAC,KAAK,IAAI,mBAAmB,CAAC,KAAK,EAAE;YACvD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,IAAM,YAAY,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../version';\n\nconst re = /^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;\n\n/**\n * Create a function to test an API version to see if it is compatible with the provided ownVersion.\n *\n * The returned function has the following semantics:\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param ownVersion version which should be checked against\n */\nexport function _makeCompatibilityCheck(\n  ownVersion: string\n): (globalVersion: string) => boolean {\n  const acceptedVersions = new Set<string>([ownVersion]);\n  const rejectedVersions = new Set<string>();\n\n  const myVersionMatch = ownVersion.match(re);\n  if (!myVersionMatch) {\n    // we cannot guarantee compatibility so we always return noop\n    return () => false;\n  }\n\n  const ownVersionParsed = {\n    major: +myVersionMatch[1],\n    minor: +myVersionMatch[2],\n    patch: +myVersionMatch[3],\n    prerelease: myVersionMatch[4],\n  };\n\n  // if ownVersion has a prerelease tag, versions must match exactly\n  if (ownVersionParsed.prerelease != null) {\n    return function isExactmatch(globalVersion: string): boolean {\n      return globalVersion === ownVersion;\n    };\n  }\n\n  function _reject(v: string) {\n    rejectedVersions.add(v);\n    return false;\n  }\n\n  function _accept(v: string) {\n    acceptedVersions.add(v);\n    return true;\n  }\n\n  return function isCompatible(globalVersion: string): boolean {\n    if (acceptedVersions.has(globalVersion)) {\n      return true;\n    }\n\n    if (rejectedVersions.has(globalVersion)) {\n      return false;\n    }\n\n    const globalVersionMatch = globalVersion.match(re);\n    if (!globalVersionMatch) {\n      // cannot parse other version\n      // we cannot guarantee compatibility so we always noop\n      return _reject(globalVersion);\n    }\n\n    const globalVersionParsed = {\n      major: +globalVersionMatch[1],\n      minor: +globalVersionMatch[2],\n      patch: +globalVersionMatch[3],\n      prerelease: globalVersionMatch[4],\n    };\n\n    // if globalVersion has a prerelease tag, versions must match exactly\n    if (globalVersionParsed.prerelease != null) {\n      return _reject(globalVersion);\n    }\n\n    // major versions must match\n    if (ownVersionParsed.major !== globalVersionParsed.major) {\n      return _reject(globalVersion);\n    }\n\n    if (ownVersionParsed.major === 0) {\n      if (\n        ownVersionParsed.minor === globalVersionParsed.minor &&\n        ownVersionParsed.patch <= globalVersionParsed.patch\n      ) {\n        return _accept(globalVersion);\n      }\n\n      return _reject(globalVersion);\n    }\n\n    if (ownVersionParsed.minor <= globalVersionParsed.minor) {\n      return _accept(globalVersion);\n    }\n\n    return _reject(globalVersion);\n  };\n}\n\n/**\n * Test an API version to see if it is compatible with this API.\n *\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param version version of the API requesting an instance of the global API\n */\nexport const isCompatible = _makeCompatibilityCheck(VERSION);\n"]}