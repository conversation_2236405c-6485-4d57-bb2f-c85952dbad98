{"name": "gaxios", "version": "6.7.1", "description": "A simple common HTTP client specifically for Google APIs and services.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src"], "scripts": {"lint": "gts check", "test": "c8 mocha build/test", "presystem-test": "npm run compile", "system-test": "mocha build/system-test --timeout 80000", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "webpack": "webpack", "prebrowser-test": "npm run compile", "browser-test": "node build/browser-test/browser-test-runner.js", "docs": "compodoc src/", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "repository": "googleapis/gaxios", "keywords": ["google"], "engines": {"node": ">=14"}, "author": "Google, LLC", "license": "Apache-2.0", "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@compodoc/compodoc": "1.1.19", "@types/cors": "^2.8.6", "@types/express": "^4.16.1", "@types/extend": "^3.0.1", "@types/mocha": "^9.0.0", "@types/multiparty": "0.0.36", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^20.0.0", "@types/node-fetch": "^2.5.7", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "@types/uuid": "^10.0.0", "abort-controller": "^3.0.0", "assert": "^2.0.0", "browserify": "^17.0.0", "c8": "^8.0.0", "cheerio": "1.0.0-rc.10", "cors": "^2.8.5", "execa": "^5.0.0", "express": "^4.16.4", "form-data": "^4.0.0", "gts": "^5.0.0", "is-docker": "^2.0.0", "karma": "^6.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.0", "karma-remap-coverage": "^0.1.5", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "5.0.0", "linkinator": "^3.0.0", "mocha": "^8.0.0", "multiparty": "^4.2.1", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^13.0.0", "null-loader": "^4.0.0", "puppeteer": "^19.0.0", "sinon": "^18.0.0", "stream-browserify": "^3.0.0", "tmp": "0.2.3", "ts-loader": "^8.0.0", "typescript": "^5.1.6", "webpack": "^5.35.0", "webpack-cli": "^4.0.0"}, "dependencies": {"extend": "^3.0.2", "https-proxy-agent": "^7.0.1", "is-stream": "^2.0.0", "node-fetch": "^2.6.9", "uuid": "^9.0.1"}}