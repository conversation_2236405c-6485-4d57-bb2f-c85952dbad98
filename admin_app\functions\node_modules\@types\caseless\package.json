{"name": "@types/caseless", "version": "0.12.5", "description": "TypeScript definitions for caseless", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/caseless", "license": "MIT", "contributors": [{"name": "downace", "githubUsername": "downace", "url": "https://github.com/downace"}, {"name": "<PERSON>", "githubUsername": "mastermatt", "url": "https://github.com/mastermatt"}, {"name": "<PERSON>", "githubUsername": "forivall", "url": "https://github.com/forivall"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/caseless"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "89139c26e367c4adc78c0db606bf3e7b208f60498430b2c167551f7169d0b81c", "typeScriptVersion": "4.5"}